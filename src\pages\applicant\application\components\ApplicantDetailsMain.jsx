import { yupResolver } from '@hookform/resolvers/yup';
import {
  Box,
  Stack
} from 'common/components';
import { StepperButtons, ConsentPopup } from 'common/custom-components';
import { useForm } from 'react-hook-form';
import { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { t } from 'i18next';
import { YES_OR_NO } from 'pages/common/constant';
import { getScholarshipTypeId, getAadhaarVerificationConsent, getOtp } from 'pages/common/selectors';
import { actions as commonActions } from 'pages/common/slice';
import { useSavePersonalDetailsMutation, useUpdatePersonalDetailsMutation } from '../api';
import {
  COMBINED_DEFAULT_VALUES,
  combinedApplicantDetailsSchema
} from '../validation/applicantDetailsSchema';
import AdditionalDetails from './AdditionalDetails';
import ApplicantDetails from './ApplicantDetails';
import { isNameMatchingAadhaarFromOtp, transformFormData } from '../helpers';
import { FAMILY_CIRCUMSTANCES } from '../constants';

const ApplicantDetailsMain = ({
  onNext, applicationDetails, isDetailsSuccess, applicationId
}) => {
  const dispatch = useDispatch();

  const scholarshipTypeId = useSelector(getScholarshipTypeId);
  const hasConsent = useSelector(getAadhaarVerificationConsent);
  const otpState = useSelector(getOtp);

  // State for consent popup
  const [showConsentPopup, setShowConsentPopup] = useState(false);

  const [savePersonalDetails, {
    isLoading: isSaveLoading, isSuccess: isSaveSuccess
  }] = useSavePersonalDetailsMutation();
  const [updatePersonalDetails, {
    isLoading: isUpdateLoading, isSuccess: isUpdateSuccess
  }] = useUpdatePersonalDetailsMutation();

  const isLoading = isSaveLoading || isUpdateLoading;
  const isSuccess = isSaveSuccess || isUpdateSuccess;

  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
    watch,
    trigger,
    setError,
    register,
    clearErrors,
    getValues,
    setValue
  } = useForm({
    defaultValues: COMBINED_DEFAULT_VALUES,
    resolver: yupResolver(combinedApplicantDetailsSchema),
    mode: 'onChange'
  });

  // Reset form with API data when available
  useEffect(() => {
    if (!isDetailsSuccess || !applicationDetails?.personalDetails) return;

    const {
      firstName = '',
      middleName = '',
      lastName = '',
      dateOfBirth,
      gender,
      aadhaarNumber = '',
      houseNoName = '',
      streetLocality = '',
      cityTown = '',
      district,
      state,
      pincode,
      contactNumber = '',
      emailId = '',
      keralite,
      nriParent,
      pravasiIdCardNumber = '',
      studentDisable,
      disabilityPercentage = null,
      orphan,
      singleParentAndBedridden,
      singleParent,
      bothParentsAndBedridden,
      sportsArtsAchievement,
      aadhaarVaultNo,
      nameAsAadhaar
    } = applicationDetails?.personalDetails || {};

    const toYesNo = (val) => (val ? YES_OR_NO.YES : YES_OR_NO.NO);

    const getFamilyCircumstances = () => {
      if (orphan) return FAMILY_CIRCUMSTANCES.ORPHAN;
      if (singleParentAndBedridden) return FAMILY_CIRCUMSTANCES.SINGLE_PARENT_BEDRIDDEN;
      if (singleParent) return FAMILY_CIRCUMSTANCES.SINGLE_PARENT_HOUSEHOLD;
      if (bothParentsAndBedridden) return FAMILY_CIRCUMSTANCES.BOTH_PARENTS_BEDRIDDEN;
      return '';
    };

    const formValues = {
      // Personal Details
      firstName,
      middleName,
      lastName,
      dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
      gender: gender?.id || '',
      aadhaarNumber,

      // Address Details
      houseNumber: houseNoName,
      streetLocality,
      cityTown,
      district: district?.id || '',
      state: state?.id || '',
      pincode: pincode ? String(pincode) : '',

      // Contact Details
      mobileNumber: contactNumber,
      emailId,

      // Additional Details
      isResident: toYesNo(keralite),
      isNriParent: toYesNo(nriParent),
      pravasiIdCardNumber,
      isDifferentlyAbled: toYesNo(studentDisable),
      percentageOfDisability: disabilityPercentage,
      familyCircumstances: getFamilyCircumstances(),
      hasRepresentedAtStateLevel: toYesNo(sportsArtsAchievement)
    };

    reset({
      ...COMBINED_DEFAULT_VALUES,
      ...formValues
    });

    dispatch(commonActions.setAadhaarVerificationConsent(true));

    dispatch(commonActions.setOtpState({
      key: aadhaarNumber,
      data: {
        isVerified: true,
        otpSuccess: false,
        verificationData: {
          uidVault: aadhaarVaultNo,
          name: nameAsAadhaar
        }
      }
    }));
  }, [isDetailsSuccess, applicationDetails?.personalDetails, reset, dispatch]);

  useEffect(() => {
    if (!hasConsent && !applicationId) {
      setShowConsentPopup(true);
    } else if (!hasConsent && applicationId) {
      dispatch(commonActions.setAadhaarVerificationConsent(true));
    }
  }, [hasConsent, dispatch, applicationId]);

  const showToast = (messageKey) => {
    dispatch(commonActions.setCustomToast({
      open: true,
      variant: 'error',
      title: t('error'),
      message: t(messageKey)
    }));
  };

  const onSubmit = (data) => {
    if (!hasConsent && !applicationId) {
      setShowConsentPopup(true);
      return;
    }

    const apiData = transformFormData(data, scholarshipTypeId, otpState);

    // Not a Keralite check
    if (apiData.keralite === false) {
      showToast('keralaOriginRequired');
      return;
    }

    // Aadhaar vault check
    if (!apiData.aadhaarVaultNo) {
      showToast('noAadhaarVaultId');
      return;
    }

    const enteredFullName = [apiData.firstName, apiData.middleName, apiData.lastName]
      .filter(Boolean)
      .join(' ');

    if (!isNameMatchingAadhaarFromOtp(enteredFullName, data.aadhaarNumber, otpState)) {
      showToast('aadhaarNameMismatch');
      return;
    }

    if (!isDirty) {
      onNext();
      return;
    }

    if (applicationId) {
      updatePersonalDetails({ id: applicationId, ...apiData });
    } else {
      savePersonalDetails(apiData);
    }
  };

  useEffect(() => {
    if (isSuccess) {
      onNext();
    }
  }, [isSuccess, onNext]);

  return (
    <Box as="form" onSubmit={handleSubmit(onSubmit)}>
      <Stack spacing={{ base: 4, md: 6 }}>
        {/* Applicant Details Section */}
        <ApplicantDetails
          control={control}
          errors={errors}
          watch={watch}
          reset={reset}
          register={register}
          setValue={setValue}
          trigger={trigger}
          setError={setError}
          clearErrors={clearErrors}
          getValues={getValues}
        />

        {/* Additional Details Section */}
        <AdditionalDetails control={control} errors={errors} watch={watch} />

        {/* Action Buttons */}
        <StepperButtons
          currentStep={0}
          totalSteps={6}
          layout="right-aligned"
          nextButtonProps={{
            isLoading,
            loadingText: t('saving')
          }}
        />
      </Stack>

      {/* Consent Popup */}
      <ConsentPopup
        isOpen={showConsentPopup}
        onClose={() => setShowConsentPopup(false)}
      />
    </Box>
  );
};

export default ApplicantDetailsMain;

import { yup<PERSON>esolver } from '@hookform/resolvers/yup';
import i18next, { t } from 'i18next';
import { useMemo } from 'react';
import * as yup from 'yup';
import { AADHAAR, MASKED_AADHAAR } from 'common/regex';

/**
 * Used for translating Schema validation translation
 * Generates a Yup resolver based on the provided schema function.
 *
 * @param {function} getSchema - A function that returns the schema object.
 * @return {function} - The Yup resolver function.
 */

const useSchema = (getSchema) => {
  const locale = i18next.language;
  const resolver = useMemo(getSchema, [locale]);
  return yupResolver(resolver);
};

/**
 * Common Aadhaar validation schema with conditional requirement
 * Validates Aadhaar number format when certain conditions are met
 *
 * @param {string|Array} whenField - Field name(s) to watch for conditional validation
 * @param {*} whenValue - Value(s) that trigger the requirement
 * @param {function} conditionFn - Custom condition function (optional, overrides whenValue)
 * @returns {yup.StringSchema} - Yup string schema with conditional Aadhaar validation
 */
export const conditionalAadhaarValidationSchema = (whenField, whenValue, conditionFn) => {
  return yup
    .string()
    .when(whenField, {
      is: conditionFn || whenValue,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('aadhaarNumber') }))
        .test(
          'aadhaar-format',
          t('fieldValidFormat', { field: t('aadhaarNumber') }),
          (value) => {
            if (!value) return false;
            return AADHAAR.test(value) || MASKED_AADHAAR.test(value);
          }
        ),
      otherwise: (schema) => schema.notRequired()
    });
};

export default useSchema;

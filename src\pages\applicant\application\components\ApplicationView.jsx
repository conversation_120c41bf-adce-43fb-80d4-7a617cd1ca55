import React from 'react';
import {
  Box,
  Text,
  HStack,
  Badge,
  Icon,
  useColorModeValue
} from 'common/components';
import {
  AccordionComponent,
  PreviewSection,
  BannerBox
} from 'common/custom-components';
import { t } from 'i18next';
import {
  UserIcon, ScholarshipTypeIcon, ApplicationNoIcon
} from 'assets/svg';
import {
  getPersonalDetailsData,
  getParentGuardianDetailsData,
  getBankDetailsData,
  getCurrentCourseDetailsData,
  getPreviousAcademicDetailsData
} from '../../../common/helpers';

const ApplicationView = ({
  applicationDetails = {},
  isDetailsSuccess = false
}) => {
  const headerBg = useColorModeValue('white', 'gray.800');
  const {
    firstName, lastName, scholarshipType, applicationStatus, applicationNumber
  } = applicationDetails?.personalDetails || {};

  // Get status color scheme
  const getStatusColorScheme = (status) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'green';
      case 'applied':
      case 'submitted':
        return 'blue';
      case 'draft':
        return 'gray';
      case 'rejected':
        return 'red';
      default:
        return 'gray';
    }
  };

  // Desktop content for BannerBox
  const desktopContent = (
    <>
      <HStack spacing={4} mb={2}>
        <Icon as={UserIcon} boxSize={5} />
        <Text fontSize="md" fontWeight="medium" color="white">
          {t('studentName')}: {firstName} {lastName}
        </Text>
      </HStack>

      <HStack spacing={4} mb={2}>
        <Icon as={ScholarshipTypeIcon} boxSize={5} />
        <Text fontSize="md" fontWeight="medium" color="white">
          {t('scholarshipType')}: {scholarshipType || 'Higher Secondary (HSS)'}
        </Text>
      </HStack>

      <HStack spacing={4}>
        <Icon as={ApplicationNoIcon} boxSize={5} />
        <Text fontSize="md" fontWeight="medium" color="white">
          {t('applicationNumber')} / {t('status')}: {applicationNumber || 'NIL'}
          <Badge
            ml={3}
            px={4}
            py={1}
            borderRadius="full"
            colorScheme={getStatusColorScheme(applicationStatus)}
            variant="solid"
            fontSize="sm"
            fontWeight="semibold"
          >
            {applicationStatus || 'NIL'}
          </Badge>
        </Text>
      </HStack>
    </>
  );

  // Header section with application info
  const renderHeader = () => (
    <BannerBox
      mobileContent={desktopContent}
      desktopContent={desktopContent}
      showImages
    />
  );

  // Combine academic details for display
  const getAcademicDetailsData = () => {
    const currentCourseData = getCurrentCourseDetailsData(applicationDetails, isDetailsSuccess);
    const previousAcademicData = getPreviousAcademicDetailsData(
      applicationDetails,
      isDetailsSuccess
    );
    return [...currentCourseData, ...previousAcademicData];
  };

  // Accordion data with preview sections
  const accordionData = [
    ...(applicationDetails?.personalDetails ? [{
      title: t('personalDetails'),
      content: (
        <PreviewSection
          data={getPersonalDetailsData(applicationDetails?.personalDetails, isDetailsSuccess)}
        />
      ),
      id: 1,
      isCompleted: false
    }] : []),
    ...(applicationDetails?.parentGuardianDetails ? [{
      title: t('parentGuardianDetails'),
      content: (
        <PreviewSection
          data={getParentGuardianDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 2,
      isCompleted: false
    }] : []),
    ...(applicationDetails?.bankDetails ? [{
      title: t('bankDetails'),
      content: (
        <PreviewSection
          data={getBankDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 3,
      isCompleted: false
    }] : []),
    ...(applicationDetails?.academicDetails ? [{
      title: t('academicDetails'),
      content: (
        <PreviewSection
          data={getAcademicDetailsData()}
        />
      ),
      id: 4,
      isCompleted: false
    }] : []),
    ...(applicationDetails?.studentDocumentsDetails ? [{
      title: t('documentUpload'),
      content: (
        <PreviewSection
          data={[
            { label: t('documentsUploaded'), value: t('viewDocuments'), colSpan: [12, 12, 12] }
          ]}
        />
      ),
      id: 5,
      isCompleted: false
    }] : [])
  ];

  return (
    <Box p={8} bg={headerBg} minH="100vh">
      {/* Header Section */}
      {renderHeader()}

      {/* Content Sections */}
      <AccordionComponent
        data={accordionData}
        allowMultiple
        currentIndexes={accordionData.map((_, index) => index)}
        isCollapsible={false}
      />
    </Box>
  );
};

export default ApplicationView;

import {
  Box,
  Flex,
  useDisclosure,
  Drawer,
  Drawer<PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>ontent,
  Drawer<PERSON>loseButton,
  DrawerBody
} from 'common/components';
import { Outlet, useLocation } from 'react-router-dom';
import { useState, useCallback, useEffect } from 'react';
import { useSelector } from 'react-redux';

import { Header, SideBar } from './components';
import { resolveBannersForRoute } from '../utils/bannerResolver';
import { getCurrentStep } from '../pages/applicant/application/selectors';

const Layout = () => {
  const sidebarDisclosure = useDisclosure();
  const location = useLocation();
  const [sidebarWidth, setSidebarWidth] = useState('250px');
  const [isSidebarExpanded, setIsSidebarExpanded] = useState(true);
  const currentStep = useSelector(getCurrentStep);

  const scrollToTop = () => {
    const container = document.querySelector('.scroll-form-hero');
    if (container) {
      setTimeout(() => {
        container.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }, 200);
    }
  };

  useEffect(() => {
    if (currentStep !== undefined && currentStep !== null) {
      scrollToTop();
    }
  }, [currentStep]);

  useEffect(() => {
    scrollToTop();
  }, [location.pathname]);

  const handleSidebarWidthChange = useCallback((width) => {
    setSidebarWidth(width);
    setIsSidebarExpanded(width === '250px');
  }, []);

  // Dynamic banner resolution using configuration
  const currentBanner = resolveBannersForRoute(location.pathname);

  return (
    <Flex direction="column" minH="100vh">
      <Header
        onSidebarToggle={sidebarDisclosure.onOpen}
        sidebarWidth={sidebarWidth}
        isSidebarExpanded={isSidebarExpanded}
      />

      <Flex flex="1" position="relative">
        {/* Sidebar for Desktop */}
        <Box display={{ base: 'none', md: 'block' }}>
          <SideBar onWidthChange={handleSidebarWidthChange} />
        </Box>

        {/* Drawer Sidebar for Mobile */}
        <Drawer
          isOpen={sidebarDisclosure.isOpen}
          placement="left"
          onClose={sidebarDisclosure.onClose}
          size="sm"
        >
          <DrawerOverlay bg="blackAlpha.600" />
          <DrawerContent maxW="280px">
            <DrawerCloseButton
              size="md"
              color="gray.600"
              _hover={{ bg: 'gray.100' }}
            />
            <DrawerBody p={0}>
              <SideBar isDrawer onClose={sidebarDisclosure.onClose} />
            </DrawerBody>
          </DrawerContent>
        </Drawer>

        <Box
          as="main"
          flex="1"
          p={6}
          overflow="auto"
          bg="gray.80"
          ml={{ base: 0, md: sidebarWidth }}
          mt={{ base: '64px', md: '64px' }}
          transition="margin-left 0.3s ease-in-out"
          minH="calc(100vh - 64px - 40px)"
          pb={{ base: '20px', md: '20px' }}
          className="scroll-form-hero"
        >
          {/* Dynamic Banner Rendering */}
          {currentBanner && (
            <currentBanner.component
              key={currentBanner.id}
              {...currentBanner.props}
            />
          )}

          <Outlet />
        </Box>
      </Flex>
      {/* Commented for future use */}
      {/* TO_DO: Footer component
        <Box
          as="footer"
          bg="white"
          color="gray.500"
          py={2}
          px={4}
          textAlign="center"
          zIndex={15}
          position="fixed"
          bottom="0"
          left="0"
          right="0"
          w="100vw"
        >
          © {new Date().getFullYear()} Norka Roots Scholar. All rights reserved.
        </Box>
      */}
    </Flex>
  );
};

export default Layout;

import {
  Box, FormController, Grid, GridItem, Heading, t, TitledCard
} from 'common/components';
import { FormLabel } from 'common/custom-components';
import React, { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { _ } from 'utils/lodash';
import { useFetchAllStateQuery, useFetchDistrictQuery, useFetchUniversitiesQuery } from 'pages/common/api';
import { STATE } from 'common/constants';
import {
  YEAR_OPTIONS, INSTITUTION_TYPE_OPTIONS, GRADING_OPTIONS,
  MARK,
  CGPA
} from '../constants';

const UndergraduateAcademicDetails = ({
  boardOptions
}) => {
  const {
    control, watch, setValue, formState: { errors }
  } = useFormContext();

  const underGradGradingSystem = watch('underGradGradingSystem') || 'MARK';
  const underGradMarksOrScore = watch('underGradMarksOrScore');
  const underGradTotalMarks = watch('underGradTotalMarks');
  const ccpaScore = watch('ccpaScore');
  const selectedState = watch('underGradStateOfInstitution');

  // Set default grading system to 'mark'
  useEffect(() => {
    if (!underGradGradingSystem || underGradGradingSystem === MARK) {
      setValue('underGradGradingSystem', MARK);
    }
  }, []);

  // Calculate percentage for marks system
  useEffect(() => {
    const score = parseFloat(underGradMarksOrScore);
    if (underGradGradingSystem === 'MARK') {
      const total = parseFloat(underGradTotalMarks);
      if (!Number.isNaN(score) && !Number.isNaN(total) && total > 0 && score <= total) {
        const percentage = (score / total) * 100;
        setValue('underGradPercentage', percentage.toFixed(2));
      } else {
        setValue('underGradPercentage', '');
      }
    } else if (underGradGradingSystem === CGPA) {
      if (!Number.isNaN(score) && score <= 10) {
        const percentage = (score * 9.5);
        setValue('underGradPercentage', percentage.toFixed(2));
      } else {
        setValue('underGradPercentage', '');
      }
    }
  }, [underGradGradingSystem, underGradMarksOrScore, underGradTotalMarks, setValue]);

  // Calculate CCPA percentage
  useEffect(() => {
    const score = parseFloat(ccpaScore);
    if (underGradGradingSystem === CGPA && !Number.isNaN(score) && score <= 10) {
      const percentage = score * 9.5;
      setValue('ccpaPercentage', percentage.toFixed(2));
    } else {
      setValue('ccpaPercentage', '');
    }
  }, [ccpaScore, underGradGradingSystem, setValue]);
  const { data: districtsData } = useFetchDistrictQuery(
    selectedState,
    { skip: !selectedState }
  );
  const { data: universitiesData } = useFetchUniversitiesQuery(
    selectedState,
    { skip: !selectedState }
  );

  const { data: allStates } = useFetchAllStateQuery();

  return (
    <TitledCard title={t('underGraduationDetails')} mt={8}>
      <Box p={6}>
        <Grid templateColumns="repeat(2, 1fr)" gap={6}>
          {/* First Row */}
          <GridItem>
            <FormController
              type="select"
              label={t('stateOfInstitution')}
              name="underGradStateOfInstitution"
              control={control}
              errors={errors}
              options={_.get(allStates, 'payload', [])}
              optionKey="id"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem>
            <FormController
              type="select"
              label={t('university')}
              name="university"
              control={control}
              errors={errors}
              options={_.get(universitiesData, 'payload', [])}
              lngOptions={{ en: 'universityName' }}
              optionKey="id"
              required
              placeholder={t('select')}
            />
          </GridItem>

          {/* Second Row */}
          <GridItem>
            <FormController
              type="select"
              label={t('institutionType')}
              name="institutionType"
              control={control}
              errors={errors}
              options={INSTITUTION_TYPE_OPTIONS}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem>
            <FormController
              type="text"
              label={t('institutionName')}
              name="instructionName"
              control={control}
              errors={errors}
              placeholder={t('institutionName')}
              required
            />
          </GridItem>

          {/* Third Row */}
          <GridItem>
            <FormController
              type="text"
              label={t('institutionLocation')}
              name="instructionLocation"
              control={control}
              errors={errors}
              optionKey="code"
              required
              placeholder={t('enterHere')}
            />
          </GridItem>

          <GridItem>
            <FormController
              type="select"
              label={t('districtOfInstitution')}
              name="underGradDistrictOfInstitution"
              control={control}
              errors={errors}
              options={_.get(districtsData, 'payload', [])}
              optionKey="id"
              required
              placeholder={t('select')}
            />

          </GridItem>

          {/* Fourth Row */}
          <GridItem>
            <FormController
              type="text"
              label={t('courseName')}
              name="underGradCourseName"
              control={control}
              errors={errors}
              placeholder={t('enterHere')}
              required
            />
          </GridItem>

          <GridItem>
            <FormController
              type="select"
              label={t('stream')}
              name="streamCriteria"
              control={control}
              errors={errors}
              options={boardOptions}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          {/* Fifth Row */}
          <GridItem>
            <FormController
              type="select"
              label={t('yearOfCompletion')}
              name="underGradYearOfCompletion"
              control={control}
              errors={errors}
              options={YEAR_OPTIONS}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem>
            <FormController
              type="text"
              label={t('registrationNumber')}
              name="registrationNumber"
              control={control}
              errors={errors}
              placeholder={t('enterHere')}
            />
          </GridItem>

          {/* Sixth Row - Full width competitive exam - Only show for specific state */}
          {selectedState !== STATE.id && (
            <GridItem colSpan={2}>
              <FormController
                type="text"
                label={t('competitiveExamAllIndiaLevel')}
                name="competitiveExam"
                control={control}
                errors={errors}
                placeholder={t('enterHere')}
              />
            </GridItem>
          )}

          {/* Grading System Section */}
          <GridItem colSpan={2} mt={4}>
            <FormLabel label={t('gradingSystem')} required />
            <Box mt={2} mb={4}>
              <FormController
                type="radio"
                name="underGradGradingSystem"
                control={control}
                errors={errors}
                options={GRADING_OPTIONS}
                optionKey="code"
                required
                direction="row"
                defaultValue="mark"
              />
            </Box>
          </GridItem>

          {/* Marks Section */}
          {underGradGradingSystem === MARK && (
            <GridItem colSpan={2}>
              <Grid templateColumns="repeat(3, 1fr)" gap={4} mt={4}>
                <GridItem>
                  <FormController
                    type="text"
                    label={t('marksObtained')}
                    name="underGradMarksOrScore"
                    control={control}
                    errors={errors}
                    placeholder={t('enterHere')}
                    required
                  />
                </GridItem>
                <GridItem>
                  <FormController
                    type="text"
                    label={t('totalMarks')}
                    name="underGradTotalMarks"
                    control={control}
                    errors={errors}
                    placeholder={t('enterHere')}
                    required
                  />
                </GridItem>
                <GridItem>
                  <FormController
                    type="text"
                    label={t('percentage')}
                    name="underGradPercentage"
                    control={control}
                    errors={errors}
                    placeholder={t('select')}
                    required
                    isReadOnly
                  />
                </GridItem>
              </Grid>
            </GridItem>
          )}

          {/* CGPA Section */}
          {underGradGradingSystem === CGPA && (
            <>
              <GridItem colSpan={2}>
                <Box mb={4}>
                  <Heading size="md" mb={3}>{t('cgpaOgpaDetails')}</Heading>
                  <Grid templateColumns="repeat(2, 1fr)" gap={4}>
                    <GridItem>
                      <FormController
                        type="text"
                        label={t('score')}
                        name="underGradMarksOrScore"
                        control={control}
                        errors={errors}
                        placeholder={t('enterHere')}
                        required
                      />
                    </GridItem>
                    <GridItem>
                      <FormController
                        type="text"
                        label={t('percentage')}
                        name="underGradPercentage"
                        control={control}
                        errors={errors}
                        placeholder=""
                        required
                        isReadOnly
                        bg="gray.100"
                      />
                    </GridItem>
                  </Grid>
                </Box>
              </GridItem>

              <GridItem colSpan={2}>
                <Box>
                  <Heading size="md" mb={3}>{t('ccpaDetailsIfApplicable')}</Heading>
                  <Grid templateColumns="repeat(2, 1fr)" gap={4}>
                    <GridItem>
                      <FormController
                        type="text"
                        label={t('score')}
                        name="ccpaScore"
                        control={control}
                        errors={errors}
                        placeholder={t('enterHere')}
                      />
                    </GridItem>
                    <GridItem>
                      <FormController
                        type="text"
                        label={t('percentage')}
                        name="ccpaPercentage"
                        control={control}
                        errors={errors}
                        placeholder=""
                        isReadOnly
                        bg="gray.100"
                      />
                    </GridItem>
                  </Grid>
                </Box>
              </GridItem>
            </>
          )}
        </Grid>
      </Box>
    </TitledCard>
  );
};

export default UndergraduateAcademicDetails;

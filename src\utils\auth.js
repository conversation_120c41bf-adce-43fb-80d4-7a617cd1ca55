import { actions as commonActions } from 'pages/common/slice';
import { ROUTE_URL } from 'common';

export const setAuthToken = (token) => {
  localStorage.setItem('authToken', token);
};

export const getAuthToken = () => {
  return localStorage.getItem('authToken');
};

export const removeAuthToken = () => {
  localStorage.removeItem('authToken');
};

export const isAuthenticated = () => {
  return !!getAuthToken();
};

export const logout = (dispatch, showMessage = false) => {
  localStorage.clear();

  if (showMessage && dispatch) {
    dispatch(commonActions.setCustomToast({
      open: true,
      variant: 'info',
      message: 'Your session has expired. Please login again.',
      title: 'Session Expired'
    }));
  }

  window.location.href = ROUTE_URL.AUTH.FULL_PATHS.LOGIN;
};

export const performLogout = () => {
  logout(null, false);
};

export const isTokenValid = () => {
  const token = localStorage.getItem('token');
  if (!token) return false;

  try {
    const parts = token.split('.');
    if (parts.length !== 3) return false;

    const payload = JSON.parse(atob(parts[1]));
    const currentTime = Math.floor(Date.now() / 1000);

    if (payload.exp && payload.exp < currentTime) {
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
};

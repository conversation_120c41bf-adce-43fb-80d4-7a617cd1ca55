import { createApi } from '@reduxjs/toolkit/query/react';
import { actions as commonActions } from 'pages/common/slice';
import { actions as fileActions } from 'pages/others/fileDownload/slice';
import { getBaseQuery } from 'utils/http';
import { _ } from 'utils/lodash';
import { REQUEST_METHOD } from 'common';
import { DEFAULT_FILE_ID } from 'common/constants';
import { t } from 'i18next';
import { RESPONSE_TYPE, STATE_REDUCER_KEY } from './constant';
import { getFileDetailsFromStream, getUrlsFileMeta } from './helpers';

export const handleFileDownload = async ({
  name, url, data = {}, baseQuery, api, isMulti = false,
  responseType = RESPONSE_TYPE.ARRAY_BUFFER,
  method = REQUEST_METHOD.GET, id = DEFAULT_FILE_ID
}) => {
  const { dispatch } = api;
  const payload = { url, responseType };

  if (method === REQUEST_METHOD.POST) {
    payload.method = method;
    payload.body = data;
  }

  try {
    const result = await baseQuery(payload, api, { responseType });
    if (result.error) return { success: false, error: result.error };
    const fileId = id || name;
    if (responseType === RESPONSE_TYPE.JSON) {
      const { data: { payload: resPayload = {} } = {} } = result;
      dispatch(fileActions.setDocuments({ [fileId]: getUrlsFileMeta(resPayload) }));
      return { fileId, data: getUrlsFileMeta(resPayload) || {}, success: true };
    }
    const formattedFile = getFileDetailsFromStream(result?.data, name);
    if (!_.isEmpty(formattedFile) && !isMulti) {
      dispatch(fileActions.setDocuments({ [fileId]: [formattedFile] }));
    }
    return {
      name, fileId, success: true, data: !isMulti ? [formattedFile] : formattedFile
    };
  } catch (error) {
    dispatch(
      commonActions.setCustomToast({
        open: true,
        variant: 'error',
        message: error.message,
        title: t('failed')
      })
    );
    return { error: { status: 'FETCH_ERROR', message: error.message }, success: false };
  }
};

export const documentApi = createApi({
  reducerPath: STATE_REDUCER_KEY.API,
  baseQuery: getBaseQuery(),
  endpoints: (builder) => ({
    downloadMultiFile: builder.mutation({
      async queryFn(payload, api, extraOptions, baseQuery) {
        const { dispatch } = api;
        const {
          url, data: dataArr = [], responseType = RESPONSE_TYPE.ARRAY_BUFFER,
          method = REQUEST_METHOD.GET, id = DEFAULT_FILE_ID
        } = payload;
        const results = await Promise.all(
          dataArr?.map((item) => {
            const newPayload = {
              name: item,
              url: url.replace(':id', item),
              data: { applicationNumber: item },
              responseType,
              method,
              id,
              isMulti: true
            };
            return handleFileDownload({
              ...newPayload, baseQuery, api, extraOptions
            });
          })
        );
        if (results.some((result) => !result.success)) {
          return { data: _.head(results)?.error, success: false, error: _.head(results)?.error };
        }
        dispatch(fileActions.setDocuments({ [id]: results.map((item) => item.data) }));
        return { data: results.map((item) => item.data) };
      }
    }),
    downloadFile: builder.mutation({
      async queryFn(payload, api, extraOptions, baseQuery) {
        return handleFileDownload({
          ...payload, baseQuery, api, extraOptions
        });
      }
    })
  })
});

export const { useDownloadMultiFileMutation, useDownloadFileMutation } = documentApi;

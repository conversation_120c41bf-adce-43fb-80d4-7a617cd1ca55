import {
  Box, CustomAlert, FormController, Grid, GridItem, t, TitledCard
} from 'common/components';
import React from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { WarningIcon } from 'assets/svg';
import { StepperButtons } from 'common/custom-components';
import { useSelector } from 'react-redux';
import { bankDetailsSchema } from '../validation/bankDetails';
import { BANK_OPTIONS, BRANCH_OPTIONS } from '../constants';
import {
  useSaveBankDetailsMutation,
  useUpdateBankDetailsMutation,
  useGetBankDetailsQuery
} from '../api';
import { getApplicationId } from '../selectors';
import { transformBankDetails } from '../helpers';

const BankDetails = ({ onNext, onPrevious }) => {
  const applicationId = useSelector(getApplicationId);
  const {
    data: bankDetailsData,
    isLoading: isBankDetailsLoading
  } = useGetBankDetailsQuery(applicationId, {
    skip: !applicationId
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm({
    mode: 'onChange',
    resolver: yupResolver(bankDetailsSchema),
    defaultValues: {
      applicationId,
      accountHolderName: '',
      bankName: '',
      branchName: '',
      accountNumber: '',
      ifscCode: ''
    }
  });

  React.useEffect(() => {
    if (bankDetailsData?.payload) {
      reset({
        ...bankDetailsData.payload,
        applicationId
      });
    }
  }, [bankDetailsData, applicationId, reset]);

  const [saveBankDetails, {
    isLoading: isSaveLoading
  }] = useSaveBankDetailsMutation();

  const [updateBankDetails, {
    isLoading: isUpdateLoading
  }] = useUpdateBankDetailsMutation();

  const isLoading = isSaveLoading || isUpdateLoading || isBankDetailsLoading;

  const onSubmit = async (data) => {
    const payload = transformBankDetails(data);

    if (bankDetailsData?.payload?.id) {
      await updateBankDetails({
        id: bankDetailsData.payload.id,
        ...payload
      }).unwrap();
    } else {
      await saveBankDetails(payload).unwrap();
    }

    if (onNext) {
      onNext(data);
    }
  };

  if (isBankDetailsLoading) {
    return <div>Loading bank details...</div>;
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <TitledCard title={t('bankDetails')}>
        <Box p={6}>
          <Box mb={10}>
            <CustomAlert
              title={t('importantNote')}
              message={t('bankAccountRequirement')}
              icon={WarningIcon}
              bg="orange.50"
              iconColor="orange.500"
              textColor="orange.700"
            />
          </Box>

          <Grid templateColumns="repeat(12, 1fr)" gap={6}>
            {/* Name of the Account Holder */}
            <GridItem colSpan={[12, 6]}>
              <FormController
                type="text"
                label={t('accountHolderName')}
                name="accountHolderName"
                control={control}
                errors={errors}
                placeholder={t('enterField', { field: t('accountHolderName') })}
                required
              />
            </GridItem>

            {/* Account Number */}
            <GridItem colSpan={[12, 6]}>
              <FormController
                type="text"
                label={t('accountNumber')}
                name="accountNumber"
                control={control}
                errors={errors}
                placeholder={t('fieldEnter', { field: t('accountNumber') })}
                required
              />
            </GridItem>

            {/* Bank Name */}
            <GridItem colSpan={[12, 6, 4]}>
              <FormController
                type="select"
                label={t('bankName')}
                name="bankName"
                control={control}
                errors={errors}
                options={BANK_OPTIONS}
                optionKey="code"
                required
                placeholder={t('select')}
              />
            </GridItem>

            {/* Branch */}
            <GridItem colSpan={[12, 6, 4]}>
              <FormController
                type="select"
                label={t('branchName')}
                name="branchName"
                control={control}
                errors={errors}
                options={BRANCH_OPTIONS}
                optionKey="code"
                required
                placeholder={t('select')}
              />
            </GridItem>

            {/* IFSC */}
            <GridItem colSpan={[12, 6, 4]}>
              <FormController
                type="text"
                label={t('ifscCode')}
                name="ifscCode"
                control={control}
                errors={errors}
                placeholder={t('fieldEnter', { field: t('ifsc') })}
                required
                transformInput={(value) => value.toUpperCase()}
              />
            </GridItem>
          </Grid>
        </Box>
      </TitledCard>

      {/* Action Buttons */}
      <Box mt={6}>
        <StepperButtons
          currentStep={4}
          totalSteps={6}
          onPrevious={onPrevious}
          layout="space-between"
          nextButtonProps={{
            isLoading,
            loadingText: t('saving')
          }}
        />
      </Box>
    </form>
  );
};

export default BankDetails;

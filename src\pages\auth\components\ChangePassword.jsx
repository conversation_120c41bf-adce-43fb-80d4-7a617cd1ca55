import {
  <PERSON>, <PERSON><PERSON>, Card, Text, VStack, Heading
} from 'common/components';
import FormController from 'common/components/FormController';
import { useForm } from 'react-hook-form';
import { Link, useLocation } from 'react-router-dom';
import { yupResolver } from '@hookform/resolvers/yup';
import { t } from 'i18next';
import { useResendOTPMutation, useResetPasswordMutation } from '../api';
import { changePasswordSchema } from '../validate';

const ChangePassword = () => {
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm({
    defaultValues: {
      newPassword: '',
      confirmPassword: '',
      otp: ''
    },
    resolver: yupResolver(changePasswordSchema),
    mode: 'all'
  });

  const location = useLocation();
  const contact = location.state?.contact;
  const UUID = location.state?.id;
  const [resetPassword] = useResetPasswordMutation();
  const [resendOTP] = useResendOTPMutation();

  const onSubmit = (data) => {
    const payload = {
      password: data.newPassword,
      confirmPassword: data.confirmPassword,
      emailOrMobile: contact,
      otp: data.otp,
      id: UUID
    };
    resetPassword(payload);
  };

  const handleResendOTP = () => {
    if (contact) {
      resendOTP(contact);
    }
  };

  return (
    <Box
      py={{ base: 4, md: '2rem' }}
      px={{ base: 4, md: '5rem' }}
      flexDirection="column"
      justifyContent="center"
      as={Card}
      display={{ base: 'block', md: 'flex' }}
      shadow={{ base: 'none', md: 'xl' }}
      borderRadius={{ base: 'none', md: '30px' }}
      maxW={{ md: '600px' }}
      mx="auto"
    >
      <VStack spacing={8} w="full" as="form" onSubmit={handleSubmit(onSubmit)}>
        <VStack spacing={4} textAlign="center">
          <Heading fontSize="3xl" fontWeight="bold" color="gray.700">
            {t('changePassword')}
          </Heading>
          <Text fontSize="md" color="gray.500">
            {t('createNewPassword')}
          </Text>
        </VStack>

        <VStack spacing={6} w="full" mt={12}>
          <FormController
            type="password"
            name="newPassword"
            control={control}
            label={t('newPassword')}
            placeholder={t('fieldEnter', { field: t('newPassword') })}
            errors={errors}
            inputHeight="50px"
          />

          <FormController
            type="password"
            name="confirmPassword"
            control={control}
            label={t('confirmPassword')}
            placeholder={t('fieldEnter', { field: t('confirmPassword') })}
            errors={errors}
            inputHeight="50px"
          />

          <FormController
            type="otp"
            name="otp"
            control={control}
            label={t('verificationCode')}
            errors={errors}
            otpProps={{
              placeholder: 'XXXXXX',
              maxLength: 6,
              isNumeric: true
            }}
          />

          <Text fontSize="sm" color="gray.500" textAlign="center">
            {t('otpSentMessage')}
          </Text>

          <Button
            variant="link"
            color="primary.A100"
            fontSize="sm"
            onClick={handleResendOTP}
            _hover={{ textDecoration: 'underline' }}
          >
            {t('resendCode')}
          </Button>

          <Button
            type="submit"
            variant="secondary"
            w="full"
            mt={{ base: 3, md: 6 }}
            isLoading={isSubmitting}
            loadingText={t('updating')}
            height="50px"
            fontSize="md"
          >
            {t('updatePassword')}
          </Button>

          <VStack spacing={1} fontSize="md" mt={6}>
            <Link
              to="/ui/auth/login"
              style={{
                color: 'primary.A100',
                textDecoration: 'underline',
                fontWeight: '500'
              }}
            >
              {t('backToLogin')}
            </Link>
          </VStack>
        </VStack>
      </VStack>
    </Box>
  );
};

export default ChangePassword;

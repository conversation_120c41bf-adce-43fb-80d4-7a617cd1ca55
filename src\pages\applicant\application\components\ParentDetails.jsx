import React, { useEffect } from 'react';
import {
  Box,
  Grid,
  GridItem,
  TitledCard,
  FormController,
  SectionHeading
} from 'common/components';
import { t } from 'i18next';
import { FormLabel } from 'common/custom-components';
import { inputHandlers } from 'utils/inputHelpers';
import { useSelector } from 'react-redux';
import { selectorWithKey } from 'utils/common';
import { getOtp } from 'pages/common/selectors';
import { getCareStatusFromCircumstances } from '../helpers';
import {
  CARE_STATUS_OPTIONS,
  RELATIONSHIP_OPTIONS,
  CURRENT_CARE_PROVIDER_OPTIONS,
  CARE_STATUS,
  CURRENT_CARE_PROVIDER
} from '../constants';

const ParentFieldGroup = ({
  prefix,
  label,
  control,
  errors,
  register,
  setValue,
  trigger,
  setError,
  clearErrors,
  getValues,
  watch
}) => {
  const watchedAadhaarNo = watch(`${prefix}AadhaarNumber`);
  const { isVerified = false } = selectorWithKey(useSelector(getOtp), watchedAadhaarNo) || {};
  return (
    <>
      <GridItem colSpan={12}>
        <SectionHeading title={label} />
      </GridItem>

      <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
        <FormController
          type="text"
          label={t('name')}
          name={`${prefix}Name`}
          control={control}
          errors={errors}
          placeholder={t('enterName')}
          required
        />
      </GridItem>

      <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
        <FormController
          type="select"
          label={t('relationshipToApplicant')}
          name={`${prefix}RelationshipToApplicant`}
          control={control}
          errors={errors}
          options={RELATIONSHIP_OPTIONS}
          optionKey="code"
          placeholder={t('select')}
          disabled
          required
        />
      </GridItem>

      <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
        <FormController
          type="number"
          label={t('contactNumber')}
          name={`${prefix}ContactNumber`}
          control={control}
          errors={errors}
          placeholder="+91"
          required
          onInput={inputHandlers.mobileWithValidation}
        />
      </GridItem>

      <FormController
        type="otpInput"
        otpType="aadhaar"
        label={t('aadhaarNumber')}
        name={`${prefix}AadhaarNumber`}
        control={control}
        errors={errors}
        register={register}
        setValue={setValue}
        trigger={trigger}
        verified={isVerified}
        setError={setError}
        clearErrors={clearErrors}
        getValues={getValues}
        colSpan={[12, 6, 6]}
        otpColSpan={[12, 6, 6]}
        placeholder={t('fieldEnter', { field: t('aadhaarNumber') })}
        required
      />
    </>
  );
};

const ParentDetails = ({
  control,
  errors,
  watch,
  applicationDetails,
  setValue,
  register,
  trigger,
  setError,
  clearErrors,
  getValues
}) => {
  const applicantCareStatus = watch('applicantCareStatus');
  const currentCareProvider = watch('currentCareProvider');

  // Watch Aadhaar numbers for verification state
  const watchedSingleParentAadhaar = watch('aadhaarNumber');
  const watchedGuardianAadhaar = watch('guardianAadhaarNumber');

  // Get verification states
  const { isVerified: isSingleParentVerified = false } = selectorWithKey(
    useSelector(getOtp),
    watchedSingleParentAadhaar
  ) || {};
  const { isVerified: isGuardianVerified = false } = selectorWithKey(
    useSelector(getOtp),
    watchedGuardianAadhaar
  ) || {};

  const isParents = applicantCareStatus === CARE_STATUS.PARENTS;
  const isSingleParent = applicantCareStatus === CARE_STATUS.SINGLE_PARENT;
  const isOrphan = applicantCareStatus === CARE_STATUS.ORPHAN;
  const isGuardian = currentCareProvider === CURRENT_CARE_PROVIDER.GUARDIAN;
  const isInstitution = currentCareProvider === CURRENT_CARE_PROVIDER.INSTITUTION;

  const predeterminedCareStatus = getCareStatusFromCircumstances(
    applicationDetails?.personalDetails
  );

  useEffect(() => {
    if (predeterminedCareStatus && applicantCareStatus !== predeterminedCareStatus) {
      setValue('applicantCareStatus', predeterminedCareStatus);
    }
  }, [predeterminedCareStatus, applicantCareStatus, setValue]);

  return (
    <TitledCard title={t('parentGuardianDetails')}>
      <Box p={{ base: 4, md: 6 }}>
        <Grid templateColumns="repeat(12, 1fr)" gap={{ base: 6, md: isParents ? 6 : 4 }}>
          {/* Applicant Care Status */}
          <GridItem colSpan={12}>
            <FormLabel label={t('applicantCareStatus')} required />
            <FormController
              type="radio"
              label={t('applicantCareStatus')}
              name="applicantCareStatus"
              control={control}
              errors={errors}
              options={CARE_STATUS_OPTIONS}
              optionKey="code"
              required
              direction="row"
              disabled
            />
          </GridItem>

          {/* Father & Mother Fields */}
          {isParents && (
            <>
              <ParentFieldGroup
                prefix="father"
                label={t('father')}
                control={control}
                errors={errors}
                register={register}
                setValue={setValue}
                trigger={trigger}
                setError={setError}
                clearErrors={clearErrors}
                getValues={getValues}
                watch={watch}
              />
              <ParentFieldGroup
                prefix="mother"
                label={t('mother')}
                control={control}
                errors={errors}
                register={register}
                setValue={setValue}
                trigger={trigger}
                setError={setError}
                clearErrors={clearErrors}
                getValues={getValues}
                watch={watch}
              />
            </>
          )}

          {/* Single Parent / Guardian Fields */}
          {isSingleParent && (
            <>
              <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
                <FormController
                  type="text"
                  label={t('parentName')}
                  name="parentName"
                  control={control}
                  errors={errors}
                  placeholder={t('fieldEnter', { field: t('parentName') })}
                  required
                />
              </GridItem>

              <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
                <FormController
                  type="select"
                  label={t('relationshipToApplicant')}
                  name="relationshipToApplicant"
                  control={control}
                  errors={errors}
                  options={RELATIONSHIP_OPTIONS}
                  optionKey="code"
                  placeholder={t('fieldSelect', { field: t('relationshipToApplicant') })}
                  required
                />
              </GridItem>
              <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
                <FormController
                  type="number"
                  label={t('contactNumber')}
                  name="contactNumber"
                  control={control}
                  errors={errors}
                  placeholder={t('fieldEnter', { field: t('contactNumber') })}
                  required
                  onInput={inputHandlers.mobileWithValidation}
                />
              </GridItem>
              <FormController
                type="otpInput"
                otpType="aadhaar"
                label={t('aadhaarNumber')}
                name="aadhaarNumber"
                control={control}
                errors={errors}
                register={register}
                setValue={setValue}
                trigger={trigger}
                verified={isSingleParentVerified}
                setError={setError}
                clearErrors={clearErrors}
                getValues={getValues}
                placeholder={t('fieldEnter', { field: t('aadhaarNumber') })}
                colSpan={[12, 6, 6]}
                otpColSpan={[12, 6, 6]}
                required
              />

            </>
          )}

          {/* Orphan - Current Care Provider */}
          {isOrphan && (
            <>
              <GridItem colSpan={{ base: 12, md: 6, lg: 4 }}>
                <FormLabel label={t('currentCareProvider')} required />
                <FormController
                  type="radio"
                  label={t('currentCareProvider')}
                  name="currentCareProvider"
                  control={control}
                  errors={errors}
                  options={CURRENT_CARE_PROVIDER_OPTIONS}
                  optionKey="code"
                  required
                  direction="row"
                />
              </GridItem>

              {/* Guardian Details */}
              {isGuardian && (
                <>
                  <GridItem colSpan={12}>
                    <SectionHeading title={t('guardianDetails')} />
                  </GridItem>

                  <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
                    <FormController
                      type="text"
                      label={t('name')}
                      name="guardianName"
                      control={control}
                      errors={errors}
                      placeholder={t('enterField', { field: t('name') })}
                      required
                    />
                  </GridItem>

                  <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
                    <FormController
                      type="text"
                      label={t('relationshipToApplicant')}
                      name="guardianRelationshipToApplicant"
                      control={control}
                      errors={errors}
                      placeholder={t('enter')}
                      required
                    />
                  </GridItem>
                  <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
                    <FormController
                      type="number"
                      label={t('contactNumber')}
                      name="guardianContactNumber"
                      control={control}
                      errors={errors}
                      placeholder="+91"
                      required
                      onInput={inputHandlers.mobileWithValidation}
                    />
                  </GridItem>
                  <FormController
                    type="otpInput"
                    otpType="aadhaar"
                    label={t('aadhaarNumber')}
                    name="guardianAadhaarNumber"
                    control={control}
                    errors={errors}
                    register={register}
                    setValue={setValue}
                    trigger={trigger}
                    verified={isGuardianVerified}
                    setError={setError}
                    clearErrors={clearErrors}
                    getValues={getValues}
                    colSpan={[12, 6, 6]}
                    otpColSpan={[12, 6, 6]}
                    placeholder={t('fieldEnter', { field: t('aadhaarNumber') })}
                    required
                  />
                </>
              )}

              {/* Institution Details */}
              {isInstitution && (
                <>
                  <GridItem colSpan={12}>
                    <SectionHeading title={t('institutionDetails')} />
                  </GridItem>

                  <GridItem colSpan={{ base: 12, md: 12, lg: 12 }}>
                    <FormController
                      type="text"
                      label={t('nameOfInstitution')}
                      name="institutionName"
                      control={control}
                      errors={errors}
                      placeholder={t('enterField', { field: t('name') })}
                      required
                    />
                  </GridItem>

                  <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
                    <FormController
                      type="text"
                      label={t('institutionRegistrationNumber')}
                      name="institutionRegistrationNumber"
                      control={control}
                      errors={errors}
                      placeholder={t('enterHere')}
                      required
                    />
                  </GridItem>

                  <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
                    <FormController
                      type="number"
                      label={t('institutionContactNumber')}
                      name="institutionContactNumber"
                      control={control}
                      errors={errors}
                      placeholder={t('enterHere')}
                      required
                      onInput={inputHandlers.mobileWithValidation}
                    />
                  </GridItem>
                  <GridItem colSpan={{ base: 12, md: 12, lg: 12 }}>
                    <FormController
                      type="text"
                      label={t('institutionAddress')}
                      name="institutionAddress"
                      control={control}
                      errors={errors}
                      placeholder={t('enterHere')}
                      required
                    />
                  </GridItem>
                </>
              )}
            </>
          )}
        </Grid>
      </Box>
    </TitledCard>
  );
};

export default ParentDetails;

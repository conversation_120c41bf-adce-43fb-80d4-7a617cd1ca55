import { createSelector } from 'reselect';

export const selector<PERSON>ithKey = createSelector(
  [
    (state) => state,
    (_state, key) => key
  ],
  (items, category) => {
    return items[category];
  }
);

export const getScholarshipTypeDisplayName = (code) => {
  const mapping = {
    HSS: 'Higher Secondary (HSS)',
    UG: 'Under Graduation (UG)',
    PG: 'Post Graduation (PG)'
  };
  return mapping[code] || code;
};

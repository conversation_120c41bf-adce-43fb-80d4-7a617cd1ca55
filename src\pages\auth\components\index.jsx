import React, { lazy } from 'react';
import {
  Kerala<PERSON>ogo, RootsLogo, LoginBg, RPLogo
} from 'assets/images';
import { useLocation } from 'react-router-dom';
import {
  Box, Flex, HStack, Image, Text, useColorModeValue, VStack
} from 'common/components';

const Login = lazy(() => import('./Login'));
const RegistrationForm = lazy(() => import('./Signup'));
const OTPVerificationPage = lazy(() => import('./Otp'));
const AccountCreatedPage = lazy(() => import('./AccountCreated'));
const ResetPassword = lazy(() => import('./ResetPassword'));
const ChangePassword = lazy(() => import('./ChangePassword'));

const AuthPage = () => {
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const contentBg = useColorModeValue('white', 'gray.800');
  const location = useLocation();

  const renderAuthComponent = () => {
    const path = location.pathname;

    if (path.endsWith('/register')) return <RegistrationForm />;
    if (path.endsWith('/login')) return <Login />;
    if (path.endsWith('/otp')) return <OTPVerificationPage />;
    if (path.endsWith('/success')) return <AccountCreatedPage />;
    if (path.endsWith('/resetPassword')) return <ResetPassword />;
    if (path.endsWith('/changePassword')) return <ChangePassword />;

    return <Login />;
  };

  return (
    <Box
      minH="100vh"
      bg={bgColor}
      backgroundImage={`url(${LoginBg})`}
      backgroundSize="cover"
      backgroundPosition="center"
      overflowY="auto"
    >
      <Box
        mx={{ base: 1, md: '8rem' }}
        my={{ base: 2, md: '1rem' }}
        position="relative"
        backdropFilter="blur(2px)"
      >
        <Box
          my={{ base: 2, md: 4 }}
          bg={contentBg}
          boxShadow={{ base: 'none', md: 'md' }}
          maxW="3500px"
          mx="auto"
          borderRadius={{ base: 'none', md: 'lg' }}
          backdropFilter="blur(2px)"
          display="flex"
          flexDirection="column"
          mt={{ base: 4, md: 8 }}
        >
          <Flex
            direction={{ base: 'column', lg: 'row' }}
            align="center"
            w="full"
            justify="center"
            bg={contentBg}
            borderRadius={8}
          >
            {/* Left Side - Information Section (hidden on small mobile) */}
            <Box
              px={{ base: 2, md: 4 }}
              display={{ base: 'none', md: 'grid' }}
              justifyItems="center"
              width="50%"
            >
              {/* Logo Section */}
              <HStack mb={{ base: 4, md: 10 }} justify="center" spacing={10}>
                <Image
                  src={KeralaLogo}
                  alt="KL_Logo"
                  height={{ base: '30px', md: '50px' }}
                  objectFit="contain"
                />
                <Image
                  src={RPLogo}
                  alt="RP_Logo"
                  height={{ base: '30px', md: '50px' }}
                  objectFit="contain"
                />
                <Image
                  src={RootsLogo}
                  alt="Roots_Logo"
                  height={{ base: '30px', md: '50px' }}
                  objectFit="contain"
                />
              </HStack>

              {/* Main Content */}
              <VStack spacing={2} textAlign="center" mb={{ base: 4, md: 6 }}>
                <Text fontSize={{ base: 'xs', md: 'md' }} color="primary.A100">
                  Unlock Your Future with Scholarships
                </Text>
                <Text
                  fontSize={{ base: 'xl', md: '4xl' }}
                  fontWeight="bold"
                  color="primary.500"
                  letterSpacing="wider"
                >
                  N O R K A
                </Text>
              </VStack>

              {/* Image Placeholder */}
              <Box
                w="80%"
                h={{ base: '150px', md: '200px' }}
                bg="gray.200"
                borderRadius="lg"
                mb={{ base: 3, md: 4 }}
                backgroundImage="url('https://media.istockphoto.com/id/1447889800/photo/two-college-student-female-friends-smiling-ready-for-classes-at-the-university-campus.jpg?s=612x612&w=0&k=20&c=IIxWOgexUu8DHDq_jPNMgPeoqsy77w9da_-9dUZeQD4=')"
                backgroundSize="cover"
                backgroundPosition="center"
                backgroundRepeat="no-repeat"
              />

              <VStack spacing={{ base: 1, md: 2 }} textAlign="center" mb={{ base: 2, md: 3 }}>
                <Text fontSize={{ base: 'xs', md: 'sm' }} color="gray.600">
                  Unlock Your Future with Scholarships
                </Text>
                <Text
                  fontSize={{ base: 'lg', md: 'xl' }}
                  fontWeight="bold"
                  color="blue.600"
                >
                  Ravi Pillai Academic Excellence Scholarship
                </Text>
              </VStack>
            </Box>

            {/* Right Side - Auth Components */}
            <Box
              px={{ base: 4, sm: 0 }}
              width={{ base: '100%', md: '50%' }}
            >
              {renderAuthComponent()}
            </Box>
          </Flex>

        </Box>
      </Box>
    </Box>
  );
};

export default AuthPage;

{"applicantDetails": "അപേക്ഷകന്റെ വിവരങ്ങൾ", "permanentAddress": "സ്ഥിര വിലാസം", "firstName": "പേരിന്റെ ആദ്യഭാഗം", "middleName": "പേരിന്റെ മധ്യഭാഗം", "lastName": "പേരിന്റെ അവസാനഭാഗം", "dateOfBirth": "ജനനത്തീയതി", "gender": "ലിംഗം", "aadhaarNumber": "ആധാർ നമ്പർ", "houseNumber": "വീട്ടുനമ്പർ/വിലാസം", "streetLocality": "തെരുവ്/പ്രദേശം", "cityTown": "നഗരം/പട്ടണം", "district": "ജില്ല", "state": "സംസ്ഥാനം", "pincode": "പിൻകോഡ്", "mobileNumber": "മൊബൈൽ നമ്പർ", "isResident": "നിങ്ങൾ ഒരു നിവാസിയാണോ?", "isNriParent": "ഈ അപേക്ഷകൻ ഒരു എൻആർഐ മാതാപിതാക്കളുടെ കുട്ടിയാണോ?", "isDifferentlyAbled": "നിങ്ങൾ വ്യത്യസ്ത കഴിവുള്ള ഒരു സ്ഥാനാർത്ഥിയാണോ (60% വൈകല്യമോ അതിൽ കൂടുതലോ)?", "additionalDetails": "അധിക വിവരങ്ങൾ (ടൈ-ബ്രേക്കറിനായി)", "isOrphan": "നിങ്ങൾ ഒരു അനാഥനാണോ?", "isSingleParentHousehold": "കിടപ്പിലായ മാതാപിതാക്കളുള്ള ഒറ്റ മാതാപിതാക്കളുടെ കുടുംബത്തിൽ പെട്ടവരാണോ?", "isFromSingleParentHousehold": "നിങ്ങൾ ഒറ്റ മാതാപിതാക്കളുടെ കുടുംബത്തിൽ നിന്നുള്ളവരാണോ?", "areBothParentsBedridden": "രണ്ട് മാതാപിതാക്കളും കിടപ്പിലാണോ/മാരകരോഗികളാണോ?", "hasRepresentedAtStateLevel": "നിങ്ങൾ സംസ്ഥാന തലത്തിൽ കായികം/കലകളിൽ പ്രതിനിധീകരിച്ചിട്ടുണ്ടോ?", "parentGuardianDetails": "മാതാപിതാക്കൾ / രക്ഷിതാവിന്റെ വിവരങ്ങൾ", "applicantCareStatus": "അപേക്ഷകൻ ആരുടെ പരിചരണത്തിലാണ്", "parentName": "മാതാപിതാക്കളുടെ പേര്", "parent": "മാതാപിതാക്കൾ", "father": "പിതാവ്", "mother": "മാതാവ്", "name": "പേര്", "select": "തിരഞ്ഞെടുക്കുക", "enterName": "പേര് നൽകുക", "enterHere": "ഇവിടെ നൽകുക", "currentCareProvider": "നിലവിലെ പരിചരണ ദാതാവ്", "guardian": "രക്ഷിതാവ്", "guardianDetails": "രക്ഷിതാവിന്റെ വിവരങ്ങൾ", "institution": "സ്ഥാപനം", "institutionDetails": "സ്ഥാപനത്തിന്റെ വിവരങ്ങൾ", "nameOfInstitution": "സ്ഥാപനത്തിന്റെ പേര്", "institutionRegistrationNumber": "സ്ഥാപന രജിസ്ട്രേഷൻ നമ്പർ", "institutionContactNumber": "സ്ഥാപന ബന്ധപ്പെടാനുള്ള നമ്പർ", "relationshipToApplicant": "അപേക്ഷകനുമായുള്ള ബന്ധം", "contactNumber": "ബന്ധപ്പെടാനുള്ള നമ്പർ", "financialDetails": "സാമ്പത്തിക വിവരങ്ങൾ", "annualFamilyIncome": "വാർഷിക കുടുംബ വരുമാനം (₹ ൽ)", "incomeCertificateIssuedBy": "വരുമാന സർട്ടിഫിക്കറ്റ് നൽകിയത്", "incomeCertificateNo": "വരുമാന സർട്ടിഫിക്കറ്റ് നമ്പർ", "certificateIssuedDate": "സർട്ടിഫിക്കറ്റ് നൽകിയ തീയതി", "sourceOfIncome": "വരുമാന സ്രോതസ്സുകൾ", "importantNote": "പ്രധാന കുറിപ്പ്", "familyIncomeCriteria": "സ്കോളർഷിപ്പിന് അർഹത നേടുന്നതിന് കുടുംബ വരുമാനം പ്രതിവർഷം ₹2.5 ലക്ഷത്തിൽ കുറവായിരിക്കണം", "emailId": "ഇമെയിൽ ഐഡി", "pravasiIdCardNumber": "പ്രവാസി ഐഡി കാർഡ് നമ്പർ", "percentageOfDisability": "വൈകല്യത്തിന്റെ ശതമാനം", "previous": "മുമ്പത്തേത്", "saveDraft": "ഡ്രാഫ്റ്റ് സേവ് ചെയ്യുക", "nextStep": "അടുത്ത ഘട്ടം", "submitApplication": "അപേക്ഷ സമർപ്പിക്കുക", "applicationNumber": "അപേക്ഷ നമ്പർ", "reviewSubmit": "അവലോകനം ചെയ്ത് സമർപ്പിക്കുക", "personalDetails": "വ്യക്തിഗത വിവരങ്ങൾ", "bankDetails": "ബാങ്ക് വിവരങ്ങൾ", "academicDetails": "അക്കാദമിക് വിവരങ്ങൾ", "documentsUpload": "ഡോക്യുമെന്റുകൾ അപ്‌ലോഡ്", "parentDetails": "മാതാപിതാക്കളുടെ വിവരങ്ങൾ", "financialInformation": "സാമ്പത്തിക വിവരങ്ങൾ", "bankName": "ബാങ്കിന്റെ പേര്", "accountNumber": "അക്കൗണ്ട് നമ്പർ", "ifscCode": "IFSC കോഡ്", "branchName": "ബ്രാഞ്ചിന്റെ പേര്", "currentCourse": "നിലവിലെ കോഴ്സ്", "institutionName": "സ്ഥാപനത്തിന്റെ പേര്", "yearOfStudy": "പഠന വർഷം", "cgpaPercentage": "CGPA/ശതമാനം", "rationCardNumber": "റേഷൻ കാർഡ് നമ്പർ", "country": "രാജ്യം", "disabilityStatus": "വൈകല്യ നില", "disabilityPercentage": "വൈകല്യ ശതമാനം", "orphanSingleParentTerminallyIll": "അനാഥ / ഒറ്റ മാതാപിതാക്കൾ / മാരകരോഗി", "applicantUnderCareOf": "അപേക്ഷകൻ ആരുടെ പരിചരണത്തിലാണ്", "relation": "ബന്ധം", "submit": "സമർപ്പിക്കുക", "cancel": "റദ്ദാക്കുക", "confirmApplication": "സമർപ്പണം സ്ഥിരീകരിക്കുക", "submitApplicationConfirmMessage": "'സ്ഥിരീകരണം' ക്ലിക്ക് ചെയ്യുന്നതിലൂടെ, നിങ്ങൾ നിങ്ങളുടെ {{scholarshipType}} നോർക്ക സ്കോളർഷിപ്പ് അപേക്ഷ സമർപ്പിക്കുകയാണ്. ഒരിക്കൽ സമർപ്പിച്ചാൽ, അപേക്ഷ എഡിറ്റ് ചെയ്യാനോ ഇല്ലാതാക്കാനോ കഴിയില്ല എന്ന് ദയവായി ശ്രദ്ധിക്കുക. നിങ്ങൾക്ക് തുടരാൻ ആഗ്രഹമുണ്ടോ?", "applicationSubmittedSuccessfully": "അപേക്ഷ വിജയകരമായി സമർപ്പിച്ചു!", "applicationSubmissionFailed": "അപേക്ഷ സമർപ്പിക്കുന്നതിൽ പരാജയപ്പെട്ടു. ദയവായി വീണ്ടും ശ്രമിക്കുക.", "address": "വിലാസം", "parentGuardianInfo": "മാതാപിതാക്കൾ/രക്ഷിതാവ് വിവരങ്ങൾ", "documents": "ഡോക്യുമെന്റുകൾ", "class10BoardExamDetails": "ക്ലാസ് 10 ബോർഡ് പരീക്ഷാ വിവരങ്ങൾ", "higherSecondaryDetails": "ഹയർ സെക്കൻഡറി വിവരങ്ങൾ", "underGraduationDetails": "ബിരുദ വിവരങ്ങൾ", "board": "ബോർഡ്", "gradePercentage": "ഗ്രേഡ്/ശതമാനം", "institutionType": "സ്ഥാപന തരം", "institutionLocation": "സ്ഥാപന സ്ഥലം", "yearOfCompletion": "പൂർത്തിയാക്കിയ വർഷം", "stateOfInstitution": "സ്ഥാപനത്തിന്റെ സംസ്ഥാനം", "districtOfInstitution": "സ്ഥാപനത്തിന്റെ ജില്ല", "courseName": "കോഴ്സിന്റെ പേര്", "stream": "സ്ട്രീം", "competitiveExam": "മത്സര പരീക്ഷ (ഓൾ ഇന്ത്യ ലെവൽ)", "gradingSystem": "ഗ്രേഡിംഗ് സിസ്റ്റം", "marksObtained": "നേടിയ മാർക്ക്", "totalMarks": "ആകെ മാർക്ക്", "percentage": "ശതമാനം", "eligibilityAlert": "യോഗ്യത മുന്നറിയിപ്പ്", "keralaOriginRequired": "ഈ സ്കോളർഷിപ്പിന് കേരള സ്വദേശികൾ മാത്രമേ യോഗ്യരാകൂ", "newApplications": "പുതിയ അപേക്ഷകൾ", "higherSecondary": "ഹയർ സെക്കൻഡറി (HSS)", "underGraduate": "ബിരുദ (UG)", "masterGraduation": "ബിരുദാനന്തര (PG)", "hssSubtitle": "ക്ലാസ് 12 അല്ലെങ്കിൽ തത്തുല്യ പരീക്ഷ പൂർത്തിയാക്കുന്ന വിദ്യാർത്ഥികൾക്ക്", "ugSubtitle": "ബിരുദ ഡിഗ്രി പ്രോഗ്രാമുകൾക്കും കോഴ്സുകൾക്കും", "pgSubtitle": "ബിരുദാനന്തര, മാസ്റ്റർ ഡിഗ്രി പ്രോഗ്രാമുകൾക്ക്", "applyNow": "ഇപ്പോൾ അപേക്ഷിക്കുക", "slNo": "ക്രമ നമ്പർ", "studentName": "വിദ്യാർത്ഥിയുടെ പേര്", "scholarshipType": "സ്കോളർഷിപ്പ് തരം", "appliedDate": "അപേക്ഷിച്ച തീയതി", "action": "പ്രവർത്തനം", "edit": "എഡിറ്റ്", "view": "കാണുക", "draft": "ഡ്രാഫ്റ്റ്", "approved": "അംഗീകരിച്ചു", "applied": "അപേക്ഷിച്ചു", "processing": "പ്രോസസ്സിംഗ്", "consentTitle": "ആധാർ സ്ഥിരീകരണ സമ്മതം", "consentMessage": "ഈ അപേക്ഷയ്ക്ക് വിദ്യാർത്ഥിയുടെയും അവരുടെ മാതാപിതാക്കളുടെ/രക്ഷിതാക്കളുടെയും ആധാർ സ്ഥിരീകരണം ആവശ്യമാണ്, നൽകിയ വിവരങ്ങളുടെ ആധികാരികത ഉറപ്പാക്കാൻ. നിങ്ങളുടെ ആധാർ വിവരങ്ങൾ സ്ഥിരീകരണ ആവശ്യങ്ങൾക്ക് മാത്രമായി ഉപയോഗിക്കുകയും ഡാറ്റ സ്വകാര്യത നിയന്ത്രണങ്ങൾക്കനുസൃതമായി കൈകാര്യം ചെയ്യുകയും ചെയ്യും.", "consentCheckboxLabel": "വിദ്യാർത്ഥിയുടെയും മാതാപിതാക്കളുടെ/രക്ഷിതാവിന്റെയും വിവരങ്ങൾ സ്ഥിരീകരിക്കുന്നതിനായി ആധാർ വിവരങ്ങൾ നൽകാൻ ഞാൻ സമ്മതിക്കുന്നു", "agreeAndContinue": "സമ്മതിച്ച് തുടരുക", "consentRequired": "തുടരുന്നതിന് സമ്മത നിബന്ധനകൾ അംഗീകരിക്കണം", "enterField": "{{field}} നൽകുക", "selectField": "{{field}} തിരഞ്ഞെടുക്കുക", "enterSelectField": "{{field}} നൽകുക/തിരഞ്ഞെടുക്കുക"}
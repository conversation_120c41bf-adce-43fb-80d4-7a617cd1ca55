import {
  Box, FormController, Grid, GridItem, Heading, t, TitledCard
} from 'common/components';
import React, { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { _ } from 'utils/lodash';
import {
  YEAR_OPTIONS, INSTITUTION_TYPE_OPTIONS
} from '../constants';

const Class10AcademicDetails = ({
  stateOptions, districtOptions, boardOptions, stateLoading, gradeOptions
}) => {
  const {
    control, watch, setValue, formState: { errors }
  } = useFormContext();

  const raw = _.get(gradeOptions, 'payload', []);
  const GradeOptions = Array.isArray(raw) ? raw : [raw];

  const marksObtained = watch('marksObtained');
  const totalMarks = watch('totalMarks');
  // const selectedBoard = watch('board');

  useEffect(() => {
    const marks = parseFloat(marksObtained);
    const total = parseFloat(totalMarks);
    if (!Number.isNaN(marks) && !Number.isNaN(total) && total > 0 && marks <= total) {
      const percentage = (marks / total) * 100;
      setValue('percentage', percentage.toFixed(2));
    } else {
      setValue('percentage', '');
    }
  }, [marksObtained, totalMarks, setValue]);

  return (
    <TitledCard title={t('academicDetails')}>
      <Box p={6}>
        <Heading size="md" mb={6} color="gray.700">
          {t('class10BoardExam')}
        </Heading>

        <Grid templateColumns="repeat(12, 1fr)" gap={6}>
          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="select"
              label={t('board')}
              name="board"
              control={control}
              errors={errors}
              options={boardOptions}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="select"
              label={t('institutionType')}
              name="institutionType"
              control={control}
              errors={errors}
              options={INSTITUTION_TYPE_OPTIONS}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="text"
              label={t('institutionName')}
              name="institutionName"
              control={control}
              errors={errors}
              placeholder={t('institutionNamePlaceholder')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="text"
              label={t('institutionLocation')}
              name="institutionLocation"
              control={control}
              errors={errors}
              placeholder={t('enterHere')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="select"
              label={t('state')}
              name="state"
              control={control}
              errors={errors}
              options={[stateOptions.payload]}
              optionKey="id"
              required
              placeholder={t('selectField', { field: t('state') })}
              disabled
              isLoading={stateLoading}
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="select"
              label={t('districtOfInstitution')}
              name="districtOfInstitution"
              control={control}
              errors={errors}
              options={_.get(districtOptions, 'payload', [])}
              optionKey="id"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="text"
              label={t('registerNumber')}
              name="registerNumber"
              control={control}
              errors={errors}
              placeholder={t('enterField', { field: t('gradePercentage') })}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="select"
              label={t('yearOfCompletion')}
              name="yearOfCompletion"
              control={control}
              errors={errors}
              options={YEAR_OPTIONS}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="select"
              label={t('grade')}
              name="streamCriteria"
              control={control}
              errors={errors}
              options={GradeOptions}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={12}>
            <Heading size="sm" mt={1} mb={1} color="gray.700">
              {t('marksDetails')}
            </Heading>
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="text"
              label={t('marksObtained')}
              name="marksObtained"
              control={control}
              errors={errors}
              placeholder={t('enterHere')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="text"
              label={t('totalMarks')}
              name="totalMarks"
              control={control}
              errors={errors}
              placeholder={t('enterHere')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="text"
              label={t('percentage')}
              name="percentage"
              control={control}
              errors={errors}
              placeholder={t('enterPercentage')}
              required
              isReadOnly
            />
          </GridItem>
        </Grid>
      </Box>
    </TitledCard>
  );
};

export default Class10AcademicDetails;

import React from 'react';
import {
  Box,
  Text,
  SimpleGrid,
  VStack
} from 'common/components';
import { ProgramCard } from 'common/custom-components';
import { SCHOLARSHIP_TYPE } from 'pages/common/constant';
import { CardImage1, CardImage2, CardImage3 } from 'assets/images';
import { t } from 'i18next';
import { useDispatch } from 'react-redux';
import { actions as commonActions } from 'pages/common/slice';
import { actions as applicationActions } from 'pages/applicant/application/slice';
import { useFetchEducationQualificationsQuery } from 'pages/common/api';

const NewApplication = ({
  title = 'newApplications',
  onApply = () => {},
  ...props
}) => {
  const dispatch = useDispatch();
  const {
    data: { payload: educationQualifications } = {}
  } = useFetchEducationQualificationsQuery();

  const applicationData = [
    {
      id: SCHOLARSHIP_TYPE.HSS,
      imageUrl: CardImage1,
      heading: t('higherSecondary'),
      subheading: t('hssSubtitle'),
      buttonText: t('apply'),
      type: 'HSS'
    },
    {
      id: SCHOLARSHIP_TYPE.UG,
      imageUrl: CardImage2,
      heading: t('underGraduate'),
      subheading: t('ugSubtitle'),
      buttonText: t('apply'),
      type: 'UG'
    },
    {
      id: SCHOLARSHIP_TYPE.PG,
      imageUrl: CardImage3,
      heading: t('masterGraduation'),
      subheading: t('pgSubtitle'),
      buttonText: t('apply'),
      type: 'PG'
    }
  ];

  const handleApplyClick = (data) => {
    const scholarshipTypeId = educationQualifications
      ?.find((qual) => qual.scholarshipType === data.type)?.id;

    if (!scholarshipTypeId) {
      dispatch(commonActions.setCustomToast({
        open: true,
        variant: 'error',
        message: 'Scholarship type not found',
        title: 'Error'
      }));
      return;
    }

    dispatch(applicationActions.clearAll());
    dispatch(commonActions.clearConsent());
    localStorage.removeItem('scholarshipType');
    localStorage.removeItem('scholarshipTypeId');

    dispatch(commonActions.setScholarshipType(data.id));
    localStorage.setItem('scholarshipType', data.id);
    dispatch(commonActions.setScholarshipTypeId(scholarshipTypeId));
    localStorage.setItem('scholarshipTypeId', scholarshipTypeId);
    dispatch(commonActions.navigateTo({
      to: 'ui/applicant/application'
    }));
    onApply(data);
  };

  return (
    <Box {...props}>
      {/* Title Section */}
      <VStack spacing={6} align="stretch">
        <Text
          fontSize={{ base: 'xl', md: '2xl' }}
          fontWeight="medium"
          color="primary.700"
          textAlign="left"
        >
          {t(title)}
        </Text>
        {/* Cards Grid */}
        <SimpleGrid
          columns={{ base: 1, md: 2, lg: 3 }}
          spacing={{ base: 4, md: 6 }}
          w="100%"
        >
          {applicationData.map((item) => (
            <ProgramCard
              key={item.id}
              imageUrl={item.imageUrl}
              heading={item.heading}
              subheading={item.subheading}
              buttonText={item.buttonText}
              onButtonClick={() => handleApplyClick(item)}
              maxWidth="100%"
              borderRadius="16px"
            />
          ))}
        </SimpleGrid>
      </VStack>
    </Box>
  );
};

export default NewApplication;

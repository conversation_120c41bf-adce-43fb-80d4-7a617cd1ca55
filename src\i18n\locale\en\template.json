{"acknowledgementMessage": "Request for {{module}} Application is successfully submitted.", "actionBy": "{{action}} by {{person}}", "asName": "{{type}} Name", "concatLabel": "{{label}} {{type}}", "errorMessage": "Error saving {{type}}. Please try again.", "fetchSuccessForId": "Details Fetched Successfully for {{type}}", "fieldEnter": "Enter your {{field}}", "fieldExactLength": "{{field}} must be exactly {{length}} digits", "fieldFutureDate": "{{field}} cannot be in the future", "fieldInvalidFormat": "{{field}} format is invalid", "fieldMaxLength": "{{field}} cannot exceed {{max}} characters", "fieldMaxValue": "{{field}} must not exceed {{max}}", "fieldMinAge": "You must be at least {{age}} years old", "fieldMinLength": "{{field}} must be at least {{min}} characters", "fieldMinValue": "{{field}} must be at least {{min}}", "fieldRequired": "{{field}} is required", "fieldSelect": "Select {{field}}", "fieldSelectOption": "Please select {{field}}", "fieldValidFormat": "Please enter a valid {{field}}", "fieldValidMobile": "Please enter a valid 10-digit mobile number starting with 6-9", "fieldValidPassword": "Password must be at least 8 characters", "fieldValidPercentage": "Percentage must be between {{min}} and {{max}}", "invalidType": "Invalid {{type}}", "isExists": "{{type}} already exists", "isInvalid": "{{type}} is invalid", "labelIn": "{{label}} ({{type}})", "mustBe": "{{type}} must be {{count}} {{unit}}", "mustBeAn": "{{type}} must be an integer", "mustBeAtLeast": "{{type}} must be at least {{count}} {{unit}}", "nameOf": "Name of {{type}}", "notAllowed": "{{type}} are not allowed", "otpSentToEmail": "OTP sent to {{email}}", "otpSentToMobile": "OTP sent to mobile ending with ***{{number}}", "placeholderIn": "{{placeholder}} {{type}}", "requestSubmit": "Your Service Request has been submitted to {{type}}", "shouldNotBe": "{{type}} should not be {{unit}}", "shouldNotBeGreaterThan": "{{type}} should not greater than {{count}} length", "successMessage": "{{type}} saved successfully!", "valueMustBeInBetween": "{{type}} must be between {{start}} and {{end}} {{unit}}", "welcomeBackUser": "Welcome back, {{name}}!"}
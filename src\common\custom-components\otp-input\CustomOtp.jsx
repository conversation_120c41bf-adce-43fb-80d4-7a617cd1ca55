import { Camera, Refresh, Verified } from 'assets/svg';
import {
  Button, FormController, IconButton,
  Icon,
  GridItem,
  Box,
  Flex,
  Text,
  Avatar
} from 'common/components';
import { OTP_TYPES } from 'common/constants';
import { EMAIL, MOBILE, AADHAAR } from 'common/regex';
import { t } from 'i18next';
import { useEffect, useState } from 'react';
import colors from 'theme/foundations/colors';
import { inputHandlers } from 'utils/inputHelpers';

const { MOBILE: MOBILE_TYPE, EMAIL: EMAIL_TYPE, AADHAAR: AADHAAR_TYPE } = OTP_TYPES;

const CustomOtp = (props) => {
  const {
    name = 'mobile',
    control,
    errors,
    labelInput,
    sendOtpBtnLabel = 'getOTP',
    containerClass = '',
    disabled = false,
    labelResendOtp = 'resendOTP',
    verifyOtpBtnLabel = 'submit',
    verifyInProgress = false,
    verified = false,
    trigger,
    setError,
    clearErrors,
    getValues,
    setValue,
    onSend = (request) => request,
    onVerify = (request) => request,
    onReSend = (request) => request,
    otpInProgress = false,
    otpLength = 6,
    required = false,
    timerDuration = 60,
    containerStyles,
    otpType = AADHAAR_TYPE,
    otpSuccess = false,
    verificationData = null,
    value,
    colSpan = [12, 6, 4],
    otpColSpan = [12, 6, 4],
    ...rest
  } = props;

  // Determine the correct label based on otpType if not provided
  const getDefaultLabel = () => {
    if (otpType === MOBILE_TYPE) return 'mobileNo';
    if (otpType === EMAIL_TYPE) return 'email';
    if (otpType === AADHAAR_TYPE) return 'aadhaarNumber';
    return 'aadhaarNumber'; // fallback
  };

  const finalLabelInput = labelInput || getDefaultLabel();

  const [timer, setTimer] = useState(0);
  const [prevVerifiedValue, setPrevVerifiedValue] = useState('');
  const [prevOtpSendValue, setPrevOtpSendValue] = useState('');
  const [isFieldEnabled, setIsFieldEnabled] = useState(true);
  const [showVerificationData, setShowVerificationData] = useState(true);

  // Simplified validation function
  const validateField = async (fieldName) => {
    if (!getValues || !setError || !clearErrors) return true;

    const fieldValue = getValues(fieldName)?.trim();

    // Get the field type label for consistent messaging
    let fieldType = '';
    if (otpType === MOBILE_TYPE) {
      fieldType = t('mobileNo');
    } else if (otpType === EMAIL_TYPE) {
      fieldType = t('email');
    } else if (otpType === AADHAAR_TYPE) {
      fieldType = t('aadhaarNumber');
    }

    if (!fieldValue) {
      setError(fieldName, { type: 'manual', message: `${fieldType} is required` });
      return false;
    }

    let isValidFormat = false;

    if (otpType === MOBILE_TYPE) {
      isValidFormat = MOBILE.test(fieldValue);
    } else if (otpType === EMAIL_TYPE) {
      isValidFormat = EMAIL.test(fieldValue);
    } else if (otpType === AADHAAR_TYPE) {
      isValidFormat = AADHAAR.test(fieldValue);
    }

    if (isValidFormat) {
      clearErrors(fieldName);
      return true;
    }

    setError(fieldName, {
      type: 'manual',
      message: t('invalidType', { type: fieldType })
    });
    return false;
  };

  // Simplified OTP send handler
  const handleOtpSend = async (isResend = false) => {
    const isValid = await validateField(name);
    if (!isValid || !getValues) return;

    // Use the same logic as currentValue to ensure consistency
    const inputValue = value || getValues(name);
    setPrevOtpSendValue(inputValue);

    const handler = isResend ? onReSend : onSend;
    handler({ [name]: inputValue });
  };

  // Simplified OTP verify handler
  const handleOtpVerify = async () => {
    if (!getValues || !setError) return;

    const otpValue = getValues(`${name}Otp`);
    if (!otpValue?.trim()) {
      setError(`${name}Otp`, { type: 'manual', message: t('Otp is required') });
      return;
    }

    const isValid = trigger ? await trigger([name, `${name}Otp`]) : true;
    if (isValid) {
      onVerify({
        [name]: getValues(name),
        [`${name}Otp`]: otpValue
      });
    }
  };

  // Simplified clear handler
  const handleClear = () => {
    setValue?.(name, '');
    setValue?.(`${name}Otp`, '');
    clearErrors?.(name);
    clearErrors?.(`${name}Otp`);
    setShowVerificationData(false);
    setIsFieldEnabled(true);
  };

  // Render button text based on state
  const getButtonText = () => {
    if (otpSuccess && prevOtpSendValue === getValues?.(name)) {
      return timer > 0 ? `${t(labelResendOtp)} (${timer}s)` : t(labelResendOtp);
    }
    return t(sendOtpBtnLabel);
  };

  // Get max length based on OTP type
  const getMaxLength = () => {
    if (otpType === EMAIL_TYPE) return 320;
    if (otpType === AADHAAR_TYPE) return 12;
    return 10; // Mobile
  };

  // Get input handler based on OTP type
  const getInputHandler = () => {
    if (otpType === AADHAAR_TYPE) return inputHandlers.aadhaar;
    if (otpType === MOBILE_TYPE) return inputHandlers.mobile;
    return undefined;
  };

  // Timer management
  useEffect(() => {
    if (otpSuccess) {
      setTimer(timerDuration);
    }
  }, [otpSuccess, timerDuration]);

  useEffect(() => {
    if (timer <= 0) return undefined;

    const interval = setInterval(() => {
      setTimer((prev) => prev - 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [timer]);

  // Handle verification state
  useEffect(() => {
    if (!verified || !getValues) return;
    const currentValue = getValues(name);
    setTimer(0);
    setValue?.(`${name}Otp`, '');
    setPrevVerifiedValue(currentValue);
  }, [verified, setValue, name, getValues]);

  useEffect(() => {
    if (prevVerifiedValue && getValues(name) === prevVerifiedValue) {
      setIsFieldEnabled(false);
      setShowVerificationData(true);
    }
  }, [prevVerifiedValue, getValues(name)]);

  // Render verification details for Aadhaar
  const renderVerificationDetails = () => {
    if (otpType !== AADHAAR_TYPE || !verified || !verificationData?.photo) {
      return null;
    }

    const {
      dob,
      name: userName,
      gender,
      photo,
      houseEng,
      streetEng,
      poEng,
      distEng,
      stateEng,
      coEng
    } = verificationData || {};

    // Format address from available fields
    const formatAddress = () => {
      const addressParts = [
        houseEng,
        streetEng,
        poEng,
        coEng
      ].filter(Boolean);

      return addressParts.length > 0 ? addressParts.join(', ') : null;
    };

    const formattedAddress = formatAddress();

    return (
      <Box
        bg="gray.80"
        borderRadius="lg"
        p={6}
        mt={4}
        boxShadow="sm"
      >
        <Flex align="center" gap={6}>
          {/* Avatar Section */}
          <Box position="relative">
            <Avatar
              size="xl"
              src={`data:image/png;base64,${photo}`}
              name={userName}
              bg="gray.200"
              color="white"
              border="4px solid white"
              boxShadow="md"
            />
            <Box
              position="absolute"
              bottom="0"
              left="50%"
              transform="translateX(-50%)"
              borderRadius="full"
              boxShadow="sm"
            >
              <Icon as={Camera} boxSize={8} />
            </Box>
          </Box>

          {/* Details Section */}
          <Flex flex={1} direction="column" gap={4}>
            {/* Row 1 - Name and Gender */}
            <Flex justify="space-between" gap={4}>
              <Box flex={1}>
                <Text fontSize="sm" color="gray.600" fontWeight="600" mb={0.5}>
                  {t('yourName')}
                </Text>
                <Text fontSize="md" fontWeight="500" color="gray.800">
                  {userName}
                </Text>
              </Box>
              <Box flex={1}>
                <Text fontSize="sm" color="gray.600" fontWeight="600" mb={0.5}>
                  {t('gender')}
                </Text>
                <Text fontSize="md" fontWeight="500" color="gray.800">
                  {gender}
                </Text>
              </Box>
            </Flex>

            {/* Row 2 - DOB and Address */}
            <Flex justify="space-between" gap={4}>
              <Box flex={1}>
                <Text fontSize="sm" color="gray.600" fontWeight="600" mb={0.5}>
                  {t('dateOfBirth')}
                </Text>
                <Text fontSize="md" fontWeight="500" color="gray.800">
                  {dob}
                </Text>
              </Box>
              <Box flex={1}>
                <Text fontSize="sm" color="gray.600" fontWeight="600" mb={0.5}>
                  {t('address')}
                </Text>
                <Text
                  fontSize="md"
                  fontWeight="500"
                  color="gray.800"
                  noOfLines={2}
                  title={formattedAddress}
                >
                  {formattedAddress || `${distEng || ''} ${stateEng || ''}`.trim() || 'N/A'}
                </Text>
              </Box>
            </Flex>
          </Flex>
        </Flex>
      </Box>

    );
  };

  // Render right content for input field
  const renderRightContent = () => {
    if (verified && prevVerifiedValue === getValues?.(name)) {
      return (
        <>
          <Icon as={Verified} boxSize={7} />
          <IconButton
            variant="primary_light"
            size="xl"
            ml={2}
            minH={10}
            minW="80px"
            maxW="100px"
            onClick={handleClear}
            icon={<Refresh size={20} color={colors.primary[500]} />}
          />
        </>
      );
    }

    return (
      <Button
        variant="primary_light"
        size="sm"
        onClick={() => handleOtpSend(otpSuccess)}
        isLoading={otpInProgress}
        disabled={otpSuccess && timer > 0 && prevOtpSendValue === getValues?.(name)}
        minW="120px"
        maxW="180px"
        px={3}
        whiteSpace="nowrap"
        overflow="hidden"
        textOverflow="ellipsis"
      >
        {getButtonText()}
      </Button>
    );
  };

  return (
    <>
      {/* Main input field */}
      <GridItem colSpan={colSpan} className={containerClass} {...containerStyles}>
        <FormController
          name={name}
          label={t(finalLabelInput)}
          placeholder={t('enter')}
          control={control}
          errors={errors}
          type="text"
          maxLength={otpType === EMAIL_TYPE ? getMaxLength() : undefined}
          disabled={disabled || !isFieldEnabled}
          required={required}
          rightContent={renderRightContent()}
          onInput={getInputHandler()}
          {...rest}
        />
      </GridItem>
      {/* OTP verification field */}
      {!verified && otpSuccess && prevOtpSendValue === getValues?.(name) && (
      <GridItem colSpan={otpColSpan}>
        <FormController
          name={`${name}Otp`}
          label={t('otp')}
          placeholder={t('enter')}
          control={control}
          errors={errors}
          required={required}
          maxLength={otpLength}
          disabled={disabled}
          rightContent={(
            <Button
              variant="primary_light"
              size="sm"
              isDisabled={otpInProgress}
              onClick={handleOtpVerify}
              isLoading={verifyInProgress}
              minW="120px"
              maxW="180px"
              px={3}
              whiteSpace="nowrap"
              overflow="hidden"
              textOverflow="ellipsis"
            >
              {t(verifyOtpBtnLabel)}
            </Button>
          )}
        />
      </GridItem>
      )}
      {/* Aadhaar Verification Details */}
      {otpType === AADHAAR_TYPE && showVerificationData && verified && verificationData && (
      <GridItem colSpan={[12, 12, 8]}>
        {renderVerificationDetails()}
      </GridItem>
      )}
    </>
  );
};

export default CustomOtp;

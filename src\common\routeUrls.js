export const ROUTE_URL = {
  NOT_FOUND: '*',
  ROOT: '/',
  BASE_PATH: 'ui',
  SAMPLE: {
    BASE: 'sample'
  },
  SCHOLAR: {
    BASE: 'scholar'
  },
  APPLICANT: {
    BASE: {
      ROOT: 'applicant',
      APPLICATION: 'applicant/application',
      APPLICATION_WITH_ID: 'applicant/application/:applicationId',
      DASHBOARD: 'applicant/dashboard',
      NEW_APPLICATION: 'applicant/new-application',
      MY_APPLICATIONS: 'applicant/my-applications'
    }
  },
  AUTH: {
    BASE: 'auth',
    LOGIN: 'login',
    REGISTER: 'register',
    OTP: 'otp',
    SUCCESS: 'success',
    RESETPASSWORD: 'resetPassword',
    CHANGEPASSWORD: 'changePassword',
    // Full paths for easy usage
    FULL_PATHS: {
      LOGIN: '/ui/auth/login',
      REGISTER: '/ui/auth/register',
      OTP: '/ui/auth/otp',
      SUCCESS: '/ui/auth/success',
      RESET_PASSWORD: '/ui/auth/resetPassword',
      CHANGE_PASSWORD: '/ui/auth/changePassword'
    }
  }
};

import React from 'react';
import {
  Box,
  Flex,
  Image
} from 'common/components';
import { Pencil } from 'assets/svg';
import { BannerCorner } from 'assets/images';

const BannerBox = ({
  children,
  showImages = true,
  bg = 'primary.500',
  borderRadius = 'lg',
  w = '100%',
  overflow = 'hidden',
  mb = 4,
  position = 'relative',
  minH = { base: '100px', md: '150px' },
  h = { md: '150px' },
  mobileContent,
  desktopContent,
  rightContent,
  ...rest
}) => {
  return (
    <Box
      bg={bg}
      borderRadius={borderRadius}
      w={w}
      overflow={overflow}
      mb={mb}
      position={position}
      minH={minH}
      h={h}
      {...rest}
    >
      {/* Mobile Layout */}
      {mobileContent && (
        <Box
          display={{ base: 'block', md: 'none' }}
          p={4}
          minH="100px"
          position="relative"
        >
          {mobileContent}
        </Box>
      )}

      {/* Desktop Layout */}
      {desktopContent && (
        <Flex
          direction={{ base: 'column', md: 'row' }}
          align="center"
          justify="space-between"
          display={{ base: 'none', md: 'flex' }}
          h={{ base: '100px', md: '150px' }}
          pl={6}
          pr={0}
          py={4}
          position="relative"
        >
          <Box maxW="60%" color="white">
            {desktopContent}
          </Box>

          {/* Right Section: Images - Positioned absolutely to remove padding */}
          {showImages && (
            <Box
              position="absolute"
              right={0}
              top={0}
              bottom={0}
              width={{ base: '100%', md: '50%' }}
              display={{ base: 'none', md: 'block' }}
              overflow="hidden"
            >
              {/* Pencil Image - Positioned to overlap slightly */}
              <Image
                src={Pencil}
                alt="Graduation Cap"
                position="absolute"
                right="180px"
                top="0"
                bottom="0"
                height="100%"
                width="auto"
                objectFit="contain"
                zIndex={2}
              />
              {/* Corner Image - Background positioned */}
              <Image
                src={BannerCorner}
                alt="Books"
                position="absolute"
                right="0"
                top="0"
                bottom="0"
                height="100%"
                width="auto"
                objectFit="contain"
                zIndex={1}
              />
            </Box>
          )}

          {/* Right Content - Positioned over the images */}
          {rightContent && (
            <Box
              position="absolute"
              right={6}
              top="50%"
              transform="translateY(-50%)"
              display={{ base: 'none', md: 'block' }}
              zIndex={3}
            >
              {rightContent}
            </Box>
          )}
        </Flex>
      )}

      {/* Fallback: If no mobile/desktop content provided, render children */}
      {!mobileContent && !desktopContent && children && (
        <Box p={6} position="relative" zIndex={1}>
          {children}

          {/* Images for fallback mode */}
          {showImages && (
            <Box
              position="absolute"
              right={0}
              top={0}
              bottom={0}
              width={{ base: '100%', md: '50%' }}
              display={{ base: 'none', md: 'block' }}
              overflow="hidden"
              zIndex={0}
            >
              <Image
                src={Pencil}
                alt="Graduation Cap"
                position="absolute"
                right="180px"
                top="0"
                bottom="0"
                height="100%"
                width="auto"
                objectFit="contain"
                zIndex={2}
              />
              <Image
                src={BannerCorner}
                alt="Books"
                position="absolute"
                right="0"
                top="0"
                bottom="0"
                height="100%"
                width="auto"
                objectFit="contain"
                zIndex={1}
              />
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
};

export default BannerBox;

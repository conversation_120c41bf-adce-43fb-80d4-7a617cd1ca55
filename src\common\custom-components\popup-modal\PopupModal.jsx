import React from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  // ModalCloseButton,
  Box,
  Flex,
  Text,
  IconButton,
  Spinner,
  useColorModeValue
} from 'common/components';
import { CloseIcon, DownloadIcon, PrintIcon } from 'assets/svg';
import colors from 'theme/foundations/colors';

const PopupModal = ({
  isOpen = false,
  onClose = () => {},
  title = '',
  children,
  size = '6xl',
  loading = false,
  headerProps = {},
  bodyProps = {},
  contentProps = {},
  overlayProps = {},
  showCloseButton = true,
  closeOnOverlayClick = true,
  closeOnEsc = true,
  isCentered = true,
  scrollBehavior = 'inside',
  showDownloadButton = false,
  showPrintButton = true,
  onDownload = () => {},
  onPrint = () => {},
  customHeaderActions = null,
  headerActionProps = {},
  ...rest
}) => {
  const headerBg = useColorModeValue('white', 'gray.800');
  const bodyBg = useColorModeValue('gray.50', 'gray.900');

  const handleClose = () => {
    if (loading) return; // Prevent closing while loading
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      size={size}
      isCentered={isCentered}
      closeOnOverlayClick={closeOnOverlayClick && !loading}
      closeOnEsc={closeOnEsc && !loading}
      scrollBehavior={scrollBehavior}
      {...rest}
    >
      <ModalOverlay
        bg="blackAlpha.600"
        backdropFilter="blur(4px)"
        {...overlayProps}
      />
      <ModalContent
        maxH="90vh"
        overflow="hidden"
        borderRadius="xl"
        boxShadow="2xl"
        {...contentProps}
      >
        {/* Header */}
        {(title || showCloseButton || showDownloadButton
          || showPrintButton || customHeaderActions) && (
          <ModalHeader
            bg={headerBg}
            py={4}
            px={6}
            {...headerProps}
          >
            <Flex align="center" justify="space-between">
              {/* Title Section */}
              {title && (
                <Text
                  fontSize="xl"
                  fontWeight="semibold"
                  color={colors.primary[500]}
                  flex="1"
                  pr={4}
                >
                  {title}
                </Text>
              )}

              {/* Action Buttons Section */}
              <Flex align="center" gap={2} {...headerActionProps}>
                {/* Custom Header Actions */}
                {customHeaderActions}

                {/* Download Button */}
                {showDownloadButton && (
                  <IconButton
                    icon={<DownloadIcon color="black" boxSize={4} />}
                    aria-label="Download"
                    size="sm"
                    variant="ghost"
                    borderRadius="full"
                    color="gray.500"
                    _hover={{
                      bg: 'gray.100',
                      color: 'gray.700'
                    }}
                    onClick={onDownload}
                    isDisabled={loading}
                  />
                )}

                {/* Print Button */}
                {showPrintButton && (
                  <IconButton
                    icon={<PrintIcon color="black" boxSize={4} />}
                    aria-label="Print"
                    size="sm"
                    variant="ghost"
                    borderRadius="full"
                    color="gray.500"
                    _hover={{
                      bg: 'gray.100',
                      color: 'gray.700'
                    }}
                    onClick={onPrint}
                    isDisabled={loading}
                  />
                )}

                {/* Close Button */}
                {showCloseButton && (
                  <IconButton
                    icon={<CloseIcon color={colors.primary[100]} boxSize={4} />}
                    aria-label="Close modal"
                    size="sm"
                    variant="ghost"
                    borderRadius="full"
                    color="gray.500"
                    _hover={{
                      bg: 'gray.100',
                      color: 'gray.700'
                    }}
                    onClick={handleClose}
                    isDisabled={loading}
                  />
                )}
              </Flex>
            </Flex>
          </ModalHeader>
        )}

        {/* Body */}
        <ModalBody
          bg={bodyBg}
          p={0}
          overflow="auto"
          {...bodyProps}
        >
          {loading ? (
            <Flex
              direction="column"
              align="center"
              justify="center"
              minH="400px"
              gap={4}
            >
              <Spinner
                size="xl"
                color={colors.primary[500]}
                thickness="4px"
              />
              <Text
                fontSize="md"
                color="gray.600"
                fontWeight="medium"
              >
                Loading application details...
              </Text>
            </Flex>
          ) : (
            <Box>
              {children}
            </Box>
          )}
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default PopupModal;

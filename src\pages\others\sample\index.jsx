import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {
  CheckSvg, OverflowIcon, InfoSvg, EditIcon, Tick, UserIcon, Calender, UploadIcon,
  ArrowIcon, ApplicationIcon, DraftIcon, WarningIcon, BellIcon, Dashboard, GuideLines,
  MyApplication, NewApplication, Setting, UserEdit, SearchIcon, DownArrow, TrashIcon,
  Hamburger, ChevronDownIcon, InfoIcon, ChevronLeftIcon, ChevronRightIcon,
  SaveArrowIcon, CloseIcon, DoneIcon, ErrorIcon, AlertWarningIcon, PDFIcon, ExpandIcon
} from 'assets/svg';
import {
  Box, Button, CustomAlert, DocumentPreview, FormController, Grid, GridItem, IconButtonWithLabel,
  SkeletonCircle, SkeletonText, Text, TitledCard, useDisclosure
} from 'common/components';
import { _ } from 'utils/lodash';
import { useFetchCountryQuery } from 'pages/common/api';
import { actions as commonActions } from 'pages/common/slice';
import colors from 'theme/foundations/colors';
import {
  AccordionComponent, DocumentUpload, DynamicTable, Pagination
} from 'common/custom-components';
import { useDownloadMultiFileMutation } from 'pages/others/fileDownload/api';
import { getFileContent } from 'pages/others/fileDownload/helpers';
import DocumentModal from 'common/custom-components/document-preview/DocumentModal';
import { API_URL } from 'common';

const validationSchema = yup.object().shape({
  email: yup.string().email('Invalid email').required('Email is required'),
  country: yup.string().required('Country is required'),
  description: yup.string().required('Description is required'),
  disabilityStatus: yup.string().required('Please select disability status'),
  dob: yup.date().nullable().required('Date of birth is required'),
  agreeTerms: yup
    .boolean()
    .oneOf([true], 'You must agree to the terms and conditions')
});

const tableColumns = [
  { key: 'slNo', header: 'Sl No', type: 'text' },
  {
    key: 'studentName',
    header: 'Student Name',
    type: 'text',
    fontWeight: '500',
    color: 'black'
  },
  { key: 'dateOfBirth', header: 'Date of Birth', type: 'text' },
  { key: 'contactNumber', header: 'Contact Number', type: 'text' },
  { key: 'courseCategory', header: 'Course Category', type: 'text' },
  { key: 'appliedDate', header: 'Applied Date', type: 'text' },
  { key: 'status', header: 'Status', type: 'status' },
  {
    key: 'action',
    header: 'Action',
    type: 'action',
    actions: [
      {
        type: 'view',
        label: 'View',
        colorScheme: 'blue',
        variant: 'outline'
      }
    ]
  }
];

const statusConfig = {
  Draft: { color: 'gray', variant: 'subtle' },
  Approved: { color: 'green', variant: 'subtle' },
  Applied: { color: 'blue', variant: 'subtle' },
  Processing: { color: 'yellow', variant: 'subtle' }
};

const studentData = [
  {
    slNo: '01',
    studentName: 'Aleena',
    dateOfBirth: '12-21-03',
    contactNumber: '9895433370',
    courseCategory: 'Higher Secondary (HSS)',
    appliedDate: '12-12-2025',
    status: 'Draft'
  },
  {
    slNo: '02',
    studentName: 'Aleena',
    dateOfBirth: '12-21-03',
    contactNumber: '9895433370',
    courseCategory: 'Master Graduation',
    appliedDate: '12-12-2025',
    status: 'Approved'
  },
  {
    slNo: '03',
    studentName: 'Aleena',
    dateOfBirth: '12-21-03',
    contactNumber: '9895433370',
    courseCategory: 'Under Graduation',
    appliedDate: '12-12-2025',
    status: 'Applied'
  },
  {
    slNo: '04',
    studentName: 'Aleena',
    dateOfBirth: '12-21-03',
    contactNumber: '9895433370',
    courseCategory: 'Master Graduation',
    appliedDate: '12-12-2025',
    status: 'Approved'
  },
  {
    slNo: '05',
    studentName: 'Aleena',
    dateOfBirth: '12-21-03',
    contactNumber: '9895433370',
    courseCategory: 'Under Graduation',
    appliedDate: '12-12-2025',
    status: 'Processing'
  }
];

const iconList = {
  CheckSvg: <CheckSvg />,
  OverflowIcon: <OverflowIcon />,
  InfoSvg: <InfoSvg />,
  EditIcon: <EditIcon />,
  UserIcon: <UserIcon />,
  Calender: <Calender />,
  Tick: <Tick />,
  ArrowIcon: <ArrowIcon />,
  ApplicationIcon: <ApplicationIcon />,
  DraftIcon: <DraftIcon />,
  WarningIcon: <WarningIcon />,
  BellIcon: <BellIcon />,
  Dashboard: <Dashboard />,
  MyApplication: <MyApplication />,
  NewApplication: <NewApplication />,
  Setting: <Setting />,
  UserEdit: <UserEdit />,
  SearchIcon: <SearchIcon />,
  DownArrow: <DownArrow />,
  Hamburger: <Hamburger height="20px" />,
  ChevronDownIcon: <ChevronDownIcon />,
  InfoIcon: <InfoIcon />,
  ChevronLeftIcon: <ChevronLeftIcon />,
  ChevronRightIcon: <ChevronRightIcon />,
  SaveArrowIcon: <SaveArrowIcon />,
  CloseIcon: <CloseIcon />,
  DoneIcon: <DoneIcon />,
  ErrorIcon: <ErrorIcon />,
  AlertWarningIcon: <AlertWarningIcon />,
  PDFIcon: <PDFIcon />,
  ExpandIcon: <ExpandIcon />,
  UploadIcon: <UploadIcon />,
  GuideLines: <GuideLines />,
  TrashIcon: <TrashIcon />
};

const SampleCard = () => {
  const dispatch = useDispatch();
  const [currentPage, setCurrentPage] = useState(1);
  const [preview, setPreview] = useState({ aadhaarDocument: [] });
  const { isOpen, onOpen, onClose } = useDisclosure();
  const itemsPerPage = 10;
  const totalItems = 100;

  const { data: countryData, isLoading } = useFetchCountryQuery();
  // TO_DO: const [downloadFile] = useDownloadFileMutation();
  const [downloadMultiFile, { data = [] }] = useDownloadMultiFileMutation();

  const {
    control,
    handleSubmit,
    formState: { errors }, setValue
  } = useForm({
    defaultValues: {
      aadhaarDocument: null,
      country: null,
      email: '',
      description: '',
      disabilityStatus: '',
      dob: null
    },
    resolver: yupResolver(validationSchema)
  });

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const onSubmit = () => {
    dispatch(
      commonActions.setCustomToast({
        open: true,
        variant: 'success',
        message: 'Form submitted successfully!',
        title: 'Success'
      })
    );
  };

  const handleAction = (actionType, rowData) => {
    dispatch(commonActions.setCustomToast({
      open: true,
      title: `Viewing ${rowData.studentName}`,
      description: `${actionType} Details for ${rowData.courseCategory}`,
      status: 'info',
      duration: 3000,
      isClosable: true
    }));
  };

  const onFileSelect = (file, key) => {
    if (file) {
      setPreview({ [key]: [{ id: 1, ...getFileContent(file) }] });
    } else {
      setPreview({ ...preview });
    }
  };

  const onFileRemove = (key) => {
    setValue(key, null);
    setPreview({ [key]: [] });
  };

  const accordionData = [
    {
      title: 'Document Upload Section',
      content: (
        <Box p={4}>
          <DocumentUpload
            title="Important Documents"
            description="Upload your required documents here"
            fileFormats="PDF, JPEG, PNG"
            maxSize="5MB"
            showSaveDraft
          />
        </Box>
      ),
      id: 1,
      onClick: () => { },
      isCompleted: false
    },
    {
      title: 'Icons',
      content: (
        <Grid templateColumns="repeat(12, 1fr)" gap={4} p={4}>
          {Object.entries(iconList).map(([key, icon]) => (
            <GridItem colSpan={2} key={key}>
              <Text>{key}</Text>{icon}
            </GridItem>
          ))}
        </Grid>
      ),
      id: 2,
      info: 'Additional information section'
    },
    {
      title: 'Table',
      content: (
        <Box padding="6" boxShadow="lg" bg="white">
          <DynamicTable
            data={studentData}
            columns={tableColumns}
            statusConfig={statusConfig}
            onAction={handleAction}
            containerProps={{ maxWidth: '100%', overflowX: 'auto', mt: 4 }}
            tableProps={{ size: 'md', variant: 'striped' }}
          />
          <Pagination
            currentPage={currentPage}
            itemsPerPage={itemsPerPage}
            totalItems={totalItems}
            handlePageChange={handlePageChange}
            mt={4}
          />
        </Box>
      ),
      id: 3,
      isCompleted: true
    }
  ];

  if (isLoading) {
    return (
      <Box padding="6" boxShadow="lg" bg="white">
        <SkeletonCircle size="10" />
        <SkeletonText mt="4" noOfLines={4} spacing="4" skeletonHeight="2" />
      </Box>
    );
  }

  return (
    <TitledCard title="Sample Components">
      <CustomAlert />
      DocumentPreview sample
      <Button variant="primary" onClick={onOpen} mt={2}>
        Document model
      </Button>

      <DocumentModal {...{
        onOpen, onClose, isOpen, fileData: _.head(data)
      }}
      />
      <Grid templateColumns="repeat(12, 1fr)" gap={3}>
        <DocumentPreview
          action={() => downloadMultiFile({
            url: 'birth-services/cr/download/certificate-by-application-number/:id',
            data: ['CRBR-00001022-2025', 'CRBR-00000003-2025']
          })}
          zoom={0.5}
        />

        <DocumentPreview
          isMulti={false}
          id="singleFileDownload"
          payload={{
            url: API_URL.SAMPLE.BIRTH_PDF,
            name: 'BirthDocSampleDownload',
            method: 'POST',
            data: { applicationNumber: 'CRBR-00000002-2025', lbOfficeCode: 10132101224, isStatistical: false }
          }}
          zoom={0.5}
        />
        <DocumentPreview
          docList={data}
          zoom={0.3}
        />
      </Grid>
      <Box as="form" onSubmit={handleSubmit(onSubmit)} gap={8} mt={8}>
        <Grid templateColumns="repeat(12, 1fr)" gap={3}>
          {/* Country Select */}
          <GridItem colSpan={[12, 4]}>
            <FormController
              type="select"
              label="Country"
              name="country"
              control={control}
              errors={errors}
              optionKey="code"
              options={countryData || []}
              required
              isClearable
              leftContent={<UserIcon />}
            />
          </GridItem>

          {/* Email TextInput */}
          <GridItem colSpan={[12, 4]}>
            <FormController
              type="text"
              label="Email Address"
              name="email"
              control={control}
              errors={errors}
              placeholder="Enter email address"
              required
              leftContent={<UserIcon />}
            />
          </GridItem>

          {/* Description TextArea */}
          <GridItem colSpan={[12, 4]}>
            <FormController
              type="textarea"
              label="Description"
              name="description"
              control={control}
              errors={errors}
              placeholder="Type something..."
              required
            />
          </GridItem>

          {/* Disability RadioButton */}
          <GridItem colSpan={[12, 4]}>
            <FormController
              type="radio"
              label="Disability Status"
              name="disabilityStatus"
              control={control}
              errors={errors}
              optionKey="code"
              options={[
                { name: 'Parent', code: 'yes' },
                { name: 'Guardian', code: 'no' }
              ]}
              required
            />
          </GridItem>

          {/* Date Picker */}
          <GridItem colSpan={[12, 4]}>
            <FormController
              type="date"
              label="Date of Birth"
              name="dob"
              control={control}
              errors={errors}
              required
              placeholder="DD/MM/YYYY"
              fromYear={1950}
              toYear={new Date().getFullYear()}
            />
          </GridItem>
          <GridItem colSpan={[12, 4]}>
            <FormController
              type="check"
              name="agreeTerms"
              control={control}
              errors={errors}
              label="I agree to the terms and conditions"
            />
          </GridItem>
          <GridItem colSpan={[12, 12]}>
            <FormController
              type="file"
              required
              name="aadhaarDocument"
              control={control}
              showPreview
              errors={errors}
              previewData={preview.aadhaarDocument || []}
              handleChange={(file) => onFileSelect(file, 'aadhaarDocument')}
              onFileRemove={() => onFileRemove('aadhaarDocument')}
              label="Aadhar document"
              description="Government issued identity proof"
            />
          </GridItem>

          {/* Submit Button */}
          <GridItem colSpan={12} textAlign="right">
            <Button variant="primary" size="md" type="submit" mr={4}>
              Submit
            </Button>
            <IconButtonWithLabel
              label="Previous"
              icon={<ArrowIcon color={colors.primary[700]} />}
              variant="primary_light"
              iconPosition="left"
              type="submit"
              mr={4}
            />

            <IconButtonWithLabel
              label="Next Step"
              icon={<ArrowIcon direction="right" color="white" />}
              variant="primary"
              iconPosition="right"
              mr={4}
            />
            <IconButtonWithLabel
              label="Save Draft"
              icon={DraftIcon}
              variant="primary_outline"
              iconPosition="right"
            />
          </GridItem>
          <GridItem colSpan={12} textAlign="right">
            <Button variant="secondary" size="md" type="submit" mr={4}>
              Submit
            </Button>
            <IconButtonWithLabel
              label="Previous"
              icon={<ArrowIcon color={colors.secondary[700]} />}
              variant="secondary_light"
              iconPosition="left"
              type="submit"
              mr={4}
            />

            <IconButtonWithLabel
              label="Next Step"
              icon={<ArrowIcon direction="right" color="white" />}
              variant="secondary"
              iconPosition="right"
              mr={4}
            />
            <IconButtonWithLabel
              label="Save Draft"
              icon={DraftIcon}
              variant="secondary_outline"
              iconPosition="right"
            />
          </GridItem>
        </Grid>
        <AccordionComponent
          data={accordionData}
          allowMultiple={false}
          currentIndexes={[0]}
        />
      </Box>
    </TitledCard>
  );
};

export default SampleCard;

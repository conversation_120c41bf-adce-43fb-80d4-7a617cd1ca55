import {
  Box, FormController, Grid, GridItem, Heading, t, TitledCard
} from 'common/components';
import React, { useEffect, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { _ } from 'utils/lodash';
import {
  YEAR_OPTIONS, INSTITUTION_TYPE_OPTIONS, GRADES, PERCENTAGE_OPTIONS
} from '../constants';

const HigherSecondaryAcademicDetails = ({
  stateOptions, districtOptions, boardOptions, stateLoading
}) => {
  const {
    control, watch, setValue, formState: { errors }
  } = useFormContext();

  const hsMarksObtained = watch('hsMarksObtained');
  const hsTotalMarks = watch('hsTotalMarks');
  const hsBoard = watch('hsBoard');

  const hsGradeOptions = useMemo(() => {
    if (hsBoard === '01') {
      return GRADES;
    }
    return PERCENTAGE_OPTIONS;
  }, [hsBoard]);

  useEffect(() => {
    const marks = parseFloat(hsMarksObtained);
    const total = parseFloat(hsTotalMarks);
    if (!Number.isNaN(marks) && !Number.isNaN(total) && total > 0 && marks <= total) {
      const percentage = (marks / total) * 100;
      setValue('hsPercentage', percentage.toFixed(2));
    } else {
      setValue('hsPercentage', '');
    }
  }, [hsMarksObtained, hsTotalMarks, setValue]);

  return (
    <TitledCard title={t('higherSecondaryDetails')} mt={8}>
      <Box p={6}>
        <Heading size="md" mb={6} color="gray.700">
          {t('higherSecondaryExam')}
        </Heading>

        <Grid templateColumns="repeat(12, 1fr)" gap={6}>
          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="select"
              label={t('board')}
              name="hsBoard"
              control={control}
              errors={errors}
              options={boardOptions}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="select"
              label={t('institutionType')}
              name="hsInstitutionType"
              control={control}
              errors={errors}
              options={INSTITUTION_TYPE_OPTIONS}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="text"
              label={t('institutionName')}
              name="hsInstitutionName"
              control={control}
              errors={errors}
              placeholder={t('institutionNamePlaceholder')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="text"
              label={t('institutionLocation')}
              name="hsInstitutionLocation"
              control={control}
              errors={errors}
              placeholder={t('enterHere')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="select"
              label={t('state')}
              name="hsState"
              control={control}
              errors={errors}
              options={[stateOptions.payload]}
              optionKey="id"
              required
              placeholder={t('selectField', { field: t('state') })}
              disabled
              isLoading={stateLoading}
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="select"
              label={t('districtOfInstitution')}
              name="hsDistrictOfInstitution"
              control={control}
              errors={errors}
              options={_.get(districtOptions, 'payload', [])}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="text"
              label={t('hsRegisterNumber')}
              name="hsRegisterNumber"
              control={control}
              errors={errors}
              placeholder={t('enterRegistrationNumber')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="select"
              label={t('yearOfCompletion')}
              name="hsYearOfCompletion"
              control={control}
              errors={errors}
              options={YEAR_OPTIONS}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="select"
              label={t('grade')}
              name="hsGrade"
              control={control}
              errors={errors}
              options={hsGradeOptions}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={12}>
            <Heading size="sm" mt={1} mb={1} color="gray.700">
              {t('marksDetails')}
            </Heading>
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="text"
              label={t('marksObtained')}
              name="hsMarksObtained"
              control={control}
              errors={errors}
              placeholder={t('enterHere')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="text"
              label={t('totalMarks')}
              name="hsTotalMarks"
              control={control}
              errors={errors}
              placeholder={t('enterHere')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 4]}>
            <FormController
              type="text"
              label={t('percentage')}
              name="hsPercentage"
              control={control}
              errors={errors}
              placeholder={t('enterPercentage')}
              required
              isReadOnly
            />
          </GridItem>
        </Grid>
      </Box>
    </TitledCard>
  );
};

export default HigherSecondaryAcademicDetails;

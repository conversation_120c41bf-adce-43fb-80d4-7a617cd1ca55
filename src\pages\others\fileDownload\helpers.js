import { FILE_HEX_SIGNATURE } from './constant';

export const getFileDetailsFromStream = (stream, name) => {
  const uintArray = new Uint8Array(stream);
  let header = '';
  for (let i = 0; i < uintArray.length && i < 4; i += 1) {
    header += uintArray[i].toString(16);
  }
  const { ext = 'pdf', type = 'application/pdf' } = FILE_HEX_SIGNATURE[header];
  const blob = new Blob([stream], { type });
  const localURL = URL.createObjectURL(blob);
  return {
    url: localURL, type, size: blob.size, ext, name: `${name}.${ext}`
  };
};

export const getFileContent = (file) => {
  const { type, size, name } = file || {};
  const url = URL.createObjectURL(file);
  return {
    id: name, url, type, size, ext: name?.split('.').pop() || 'unknown', name
  };
};

export function getUrlsFileMeta(fileUrls) {
  const typeMap = {
    png: 'image/png',
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    pdf: 'application/pdf'
  };
  const getFileMeta = (rawUrl) => {
    try {
      const urlObj = new URL(rawUrl);
      const { pathname } = urlObj;
      const fileName = pathname.substring(pathname.lastIndexOf('/') + 1);

      const regex = /^(.*)\.([a-zA-Z0-9]+)$/;
      const match = regex.exec(fileName);
      const id = match?.[1] || fileName;
      const ext = match?.[2]?.toLowerCase() || '';
      const type = typeMap[ext] || typeMap.jpg;
      return {
        id,
        name: fileName,
        url: rawUrl,
        ext,
        type,
        size: ''
      };
    } catch (e) {
      return null;
    }
  };

  return Object.fromEntries(
    Object.entries(fileUrls).map(([key, url]) => [key, url ? getFileMeta(url) : null])
  );
}

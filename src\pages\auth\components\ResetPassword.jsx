import {
  Box, Button, Card, HStack, Text, VStack, IconButton, Heading,
  useToast
} from 'common/components';
import FormController from 'common/components/FormController';
import { useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import { BackForward } from 'assets/svg';

import { yupResolver } from '@hookform/resolvers/yup';
import { t } from 'i18next';
import { useForgotPasswordMutation } from '../api';
import { resetPasswordSchema } from '../validate';
import { CHANGE_PASSWORD_ROUTE } from '../constant';

const ResetPassword = () => {
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError
  } = useForm({
    defaultValues: {
      emailOrPhone: ''
    },
    resolver: yupResolver(resetPasswordSchema),
    mode: 'onBlur'
  });

  const toast = useToast();
  const navigate = useNavigate();
  const [forgotPassword] = useForgotPasswordMutation();

  const onSubmit = async (data) => {
    try {
      const payload = { emailOrMobile: data.emailOrPhone };
      const response = await forgotPassword(payload).unwrap();

      navigate(CHANGE_PASSWORD_ROUTE, {
        state: {
          contact: data.emailOrPhone,
          id: response.payload.otp.UUID
        }
      });
    } catch (error) {
      if (error.data?.field === 'emailOrPhone') {
        setError('emailOrPhone', {
          type: 'manual',
          message: error.data.message
        });
      } else {
        toast({
          title: t('error'),
          description: error.data?.message || t('failedToSendResetLink'),
          status: 'error',
          duration: 5000,
          isClosable: true,
          position: 'top-right'
        });
      }
    }
  };

  return (
    <Box
      py={{ base: 4, md: '2rem' }}
      px={{ base: 4, md: '5rem' }}
      flexDirection="column"
      justifyContent="center"
      as={Card}
      display={{ base: 'block', md: 'flex' }}
      shadow={{ base: 'none', md: 'xl' }}
      borderRadius={{ base: 'none', md: '30px' }}
      minH={{ md: '580px' }}
      maxW={{ md: '600px' }}
      mx="auto"
    >
      <VStack spacing={8} w="full" h="full" justify="space-between">
        <VStack w="full" spacing={8}>
          <HStack mb={{ base: 4, md: 6 }} spacing={3} align="flex-start">
            <IconButton
              icon={<BackForward />}
              variant="ghost"
              size="sm"
              aria-label="Go back"
              display={{ base: 'none', md: 'flex' }}
              onClick={() => navigate(-1)}
            />
            <VStack align="start" spacing={0}>
              <Heading fontSize="3xl" fontWeight="bold" color="gray.700">
                Reset Password
              </Heading>
              <Text fontSize="md" color="gray.500">
                Enter your email/mobile to receive a reset link
              </Text>
            </VStack>
          </HStack>

          <VStack spacing={6} w="full" as="form" onSubmit={handleSubmit(onSubmit)}>
            <FormController
              type="text"
              name="emailOrPhone"
              control={control}
              label={t('emailAddressMobileNumber')}
              placeholder={t('enterEmailOrMobile')}
              errors={errors}
              inputHeight="50px"
            />
          </VStack>
        </VStack>

        <VStack w="full" spacing={6}>
          <Button
            type="submit"
            variant="secondary"
            w="full"
            isLoading={isSubmitting}
            loadingText="Sending..."
            onClick={handleSubmit(onSubmit)}
          >
            Next
          </Button>

          <Text fontSize="md" color="gray.500" textAlign="center">
            Remember your password?{' '}
            <Link
              to="/login"
              style={{
                color: 'primary.A100',
                textDecoration: 'underline',
                fontWeight: '500'
              }}
            >
              Sign In
            </Link>
          </Text>
        </VStack>
      </VStack>
    </Box>
  );
};

export default ResetPassword;

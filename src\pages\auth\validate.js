import {
  EMAIL, EN_NUMERIC, MOBILE, PASSWORD
} from 'common/regex';
import { t } from 'i18next';
import { USER_APPLICANT, USER_OFFICIAL } from 'pages/common/constant';
import * as Yup from 'yup';

export const signUpSchema = Yup.object().shape({
  userType: Yup.string()
    .oneOf([USER_APPLICANT.code, USER_OFFICIAL.code], t('fieldSelectOption', { field: t('userType') }))
    .required(t('fieldRequired', { field: t('userType') })),

  userName: Yup.string()
    .matches(EN_NUMERIC, t('isInvalid', { type: t('userName') }))
    .min(5, t('fieldMinLength', { field: t('userName'), min: t('5') }))
    .required(t('fieldRequired', { field: t('userName') })),

  officialId: Yup.string().when('userType', {
    is: USER_OFFICIAL.code,
    then: (schema) => schema.required(t('fieldRequired', { field: t('norkaId') })),
    otherwise: (schema) => schema.notRequired()
  }),

  emailId: Yup.string()
    .email(t('isInvalid', { type: t('emailId') }))
    .when('userType', {
      is: USER_APPLICANT.code,
      then: (schema) => schema.required(t('fieldRequired', { field: t('emailId') })),
      otherwise: (schema) => schema.notRequired()
    }),

  mobileNumber: Yup.string()
    .matches(MOBILE, t('fieldValidMobile'))
    .when('userType', {
      is: USER_APPLICANT.code,
      then: (schema) => schema.required(t('fieldRequired', { field: t('mobileNumber') })),
      otherwise: (schema) => schema.notRequired()
    }),

  password: Yup.string()
    .min(8, t('fieldMinLength', { field: t('password'), min: t('8') }))
    .matches(PASSWORD, t('fieldValidPassword'))
    .required(t('fieldRequired', { field: t('password') }))
    .when('confirmPassword', {
      is: (val) => val,
      then: (schema) => schema.oneOf([Yup.ref('confirmPassword'), null], t('passwordMatch'))
    }),

  confirmPassword: Yup.string()
    .min(8, t('fieldMinLength', { field: t('password'), min: t('8') }))
    .matches(PASSWORD, t('fieldValidPassword'))
    .oneOf([Yup.ref('password'), null], t('passwordMatch'))
    .required(t('fieldRequired', { field: t('confirmPassword') }))
});

export const loginSchema = Yup.object().shape({
  emailOrMobile: Yup.string()
    .required(t('fieldRequired', { field: t('emailOrMobile') }))
    .test('email-or-mobile', t('fieldValidEmailOrMobile'), (value) => {
      if (!value) return false;
      const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      const isMobile = MOBILE.test(value);
      return isEmail || isMobile;
    }),

  password: Yup.string()
    .min(8, t('fieldMinLength', { field: t('password'), min: t('8') }))
    .required(t('fieldRequired', { field: t('password') }))
});

export const changePasswordSchema = Yup.object().shape({
  newPassword: Yup.string()
    .min(8, t('fieldMinLength', { field: t('password'), min: t('8') }))
    .matches(PASSWORD, t('fieldValidPassword'))
    .required(t('fieldRequired', { field: t('newPassword') })),

  confirmPassword: Yup.string()
    .min(8, t('fieldMinLength', { field: t('password'), min: t('8') }))
    .oneOf([Yup.ref('newPassword'), null], t('passwordMatch'))
    .required(t('fieldRequired', { field: t('confirmPassword') })),

  otp: Yup.string()
    .required(t('fieldRequired', { field: t('otp') }))
    .min(6, t('fieldMinLength', { field: t('otp'), min: t('6') }))
});

export const otpVerificationSchema = Yup.object().shape({
  otp: Yup.string()
    .required(t('fieldRequired', { field: t('otp') }))
    .test('otp-length', function validateOtpLength(value) {
      if (!value) return this.createError({ message: t('fieldRequired', { field: t('otp') }) });
      if (value.length < 6) return this.createError({ message: t('fieldMinLength', { field: t('otp'), min: t('6') }) });
      if (value.length > 6) return this.createError({ message: t('fieldMaxLength', { field: t('otp'), max: t('6') }) });
      if (!/^\d{6}$/.test(value)) return this.createError({ message: t('fieldExactLength', { field: t('otp'), length: t('6') }) });
      return true;
    })
});

export const resetPasswordSchema = Yup.object().shape({
  emailOrPhone: Yup.string()
    .required(t('fieldRequired', { field: t('emailOrMobile') }))
    .test('email-or-mobile', t('fieldValidEmailOrMobile'), (value) => {
      if (!value) return false;
      const isEmail = EMAIL.test(value);
      const isMobile = MOBILE.test(value);
      return isEmail || isMobile;
    })
});

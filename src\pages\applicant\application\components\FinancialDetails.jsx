import React from 'react';
import {
  Box, CustomAlert, FormController, Grid, GridItem, TitledCard
} from 'common/components';
import { useTranslation } from 'react-i18next';
import { FormLabel } from 'common/custom-components';
import { INCOME_CERTIFICATE_ISSUER_OPTIONS } from '../constants';

export const FinancialDetails = ({
  control,
  errors
}) => {
  const { t } = useTranslation();
  return (
    <TitledCard title={t('financialDetails')}>
      <CustomAlert />
      <Box p={{ base: 4, md: 6 }}>
        <Grid templateColumns="repeat(12, 1fr)" gap={{ base: 4, md: 6, lg: 8 }} mt={{ base: 4, md: 8 }}>
          {/* Annual Family Income */}
          <GridItem colSpan={[12, 12, 6, 4]}>
            <FormController
              type="number"
              label={t('annualFamilyIncome')}
              name="annualFamilyIncome"
              control={control}
              errors={errors}
              placeholder={t('fieldEnter', { field: t('annualFamilyIncome') })}
              required
              min={0}
              max={250000}
            />
          </GridItem>

          <GridItem colSpan={12}>
            <FormLabel label={t('incomeCertificateIssuedBy')} required />
            <FormController
              type="radio"
              label={t('incomeCertificateIssuedBy')}
              name="incomeCertificateIssuedBy"
              control={control}
              errors={errors}
              options={INCOME_CERTIFICATE_ISSUER_OPTIONS}
              optionKey="code"
              required
              direction={{ base: 'column', sm: 'row' }}
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 6, 4]}>
            <FormController
              type="text"
              label={t('incomeCertificateNo')}
              name="incomeCertificateNo"
              control={control}
              errors={errors}
              placeholder={t('fieldEnter', { field: t('incomeCertificateNo') })}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 12, 6, 4]}>
            <FormController
              type="date"
              label={t('certificateIssuedDate')}
              maxDate={new Date()}
              name="certificateIssuedDate"
              control={control}
              errors={errors}
              required
            />
          </GridItem>

        </Grid>
      </Box>
    </TitledCard>
  );
};

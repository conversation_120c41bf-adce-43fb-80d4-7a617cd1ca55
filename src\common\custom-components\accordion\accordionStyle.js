const component = {};

const accordionStyle = {
  '.chakra-accordion__item': {
    borderRadius: '8px',
    border: 'none'
  },
  '.chakra-accordion__item:first-of-type': {
    marginTop: 0
  },
  '.chakra-accordion__item:last-of-type': {
    marginBottom: 0
  },
  '.accordionButton': {
    height: '60px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'primary.80',
    marginTop: '10px',
    position: 'relative',
    borderRadius: '5px',
    '&:hover': {
      backgroundColor: 'primary.50'
    },
    '.accordionHeaderRightContent': {
      position: 'absolute',
      display: 'flex',
      width: '44px',
      height: '44px',
      justifyContent: 'center',
      backgroundColor: 'primary.A50',
      alignItems: 'center',
      borderRadius: '5px',
      top: '50%',
      right: 'calc(44px + 16px + 16px)',
      transform: 'translateY(-50%)',
      svg: {
        width: '24px',
        height: '24px'
      },
      '&.no-collapse-icon': {
        right: '16px'
      }
    },
    '.edit-icon-clickable': {
      cursor: 'pointer !important',
      transition: 'all 0.2s ease-in-out',
      '&:hover': {
        transform: 'scale(1.1)',
        opacity: '0.8'
      }
    }
  },
  '.accordionButton[aria-expanded="true"]::before': {
    content: '""',
    position: 'absolute',
    left: 0,
    width: '4px',
    height: '60px',
    backgroundColor: 'secondary.500',
    borderTopLeftRadius: '99px'
  },
  '.accordionButton[aria-expanded="true"] .accordionHeadContent': {
    fontSize: '18px'
  },
  '.accordionHeadContent': {
    padding: '0px 14px',
    fontSize: '16px',
    fontWeight: '600',
    color: 'tertiary.500'
  },
  '.accordionIcon': {
    width: '44px',
    height: '44px',
    display: 'flex',
    justifyContent: 'center',
    backgroundColor: 'primary.500',
    alignItems: 'center',
    borderRadius: '5px',
    svg: {
      width: '24px',
      height: '24px',
      fill: '#DEEDF8'
    }
  },
  '.accordionButton.non-collapsible': {
    cursor: 'default !important',
    '&:hover': {
      backgroundColor: 'primary.80 !important'
    }
  },
  '.customAccordionPanel': {
    padding: '0 !important'
  }
};

const style = {
  ...accordionStyle
};

export default { style, component };
